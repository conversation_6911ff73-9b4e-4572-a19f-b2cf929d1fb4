package id.co.bri.brimo.contract.IView.britamarencana;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.FormBukaRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryEditRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;

public interface IHitungRencanaView  extends IMvpView {
    void onSuccessGetFormRencana(FormBukaRencanaResponse formBukaRencanaResponse);

    void onSuccessInquiry(InquiryOpenRencanaResponse inquiryOpenRencanaResponse);

    void onSuccessGetFormEditRencana(FormBukaRencanaResponse formBukaRencanaResponse);

    void onSuccessInquiryEdit(InquiryEditRencanaResponse inquiryEditRencanaResponse);

    void onException12();
}
