package id.co.bri.brimo.adapters;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ListItemKodepos2Binding;
import id.co.bri.brimo.models.apimodel.response.KodePosBottomResponse;

public class ListKodePosAdapter extends RecyclerView.Adapter<ListKodePosAdapter.ViewHolder> {

    private List<KodePosBottomResponse.Result> kodePosList;
    private OnClickKodePos onClickKodePos;

    public ListKodePosAdapter(List<KodePosBottomResponse.Result> kodePosList, OnClickKodePos onClickKodePos) {
        this.kodePosList = kodePosList;
        this.onClickKodePos = onClickKodePos;
    }

    public interface OnClickKodePos {
        void onClickKodePos(KodePosBottomResponse.Result kodePosResponse);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ListItemKodepos2Binding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        KodePosBottomResponse.Result kodepos = kodePosList.get(position);
        holder.binding.tvKodepos.setText(kodepos.getLabel());
        holder.itemView.setOnClickListener(v -> onClickKodePos.onClickKodePos(kodepos));
    }

    @Override
    public int getItemCount() {
        return (kodePosList != null) ? kodePosList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ListItemKodepos2Binding binding;

        public ViewHolder(@NonNull ListItemKodepos2Binding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
