package id.co.bri.brimo.contract.IView.dplkrevamp

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailBrifineDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquirySettingAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse

interface IDetailBrifineDplkRevampView : IMvpView {

    fun onSuccessGetDetailBrfineDplk(response: DetailBrifineDplkResponse)
    fun onSuccessInquiryClaimDplk(response: GeneralConfirmationResponse)

    fun onSuccessGetPusatBantuan(response : QuestionResponse)

    fun onExceptionClaim(message: String)

    fun onSafetyMode(response : SafetyModeDrawerResponse)

    fun onSuccessGetInquiry(inquiryRevampResponse: InquiryDompetDigitalResponse)

    fun onSuccessGetHistoryDetailClaimDplk(data: ReceiptRevampResponse)
}