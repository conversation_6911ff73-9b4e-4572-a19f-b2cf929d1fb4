package id.co.bri.brimo.domain.extension

import android.app.Activity
import android.content.Context
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import java.text.DecimalFormat
import java.util.Currency
import java.util.Locale


/***
 * This file containing extension related to [EditText]
 */


/**
 * To make keyboard appear by focusing on [EditText]
 */
fun EditText.showKeyboard(activity: Activity, flags: Int = 0) {
    requestFocus()
    val keyboard = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    keyboard.showSoftInput(this, flags)
}


/**
 * To make keyboard disappear by focusing on [EditText]
 */
fun EditText.dismissKeyboard(activity: Activity) {
    val keyboard = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    keyboard.hideSoftInputFromWindow(this.windowToken, 0)
}


/**
 * To assign some IME action on [EditText] when user hit the enter button on keyboard
 */
fun EditText.setEnterAction(funct: () -> Unit) {
    setOnEditorActionListener { _, actionId, _ ->
        when (actionId) {
            EditorInfo.IME_ACTION_UNSPECIFIED,
            EditorInfo.IME_ACTION_NEXT,
            EditorInfo.IME_ACTION_SEARCH,
            EditorInfo.IME_ACTION_DONE -> {
                funct()
                true
            }
            else -> false
        }
    }
}

fun formatCurrencyText(
    value: String, locale: Locale?, defaultLocale: Locale, decimalDigits: Int? = null, showCurrencySymbol: Boolean,
    customFormatting: ((String, String) -> String)? = null
): String {
    //special case for the start of a negative number
    if (value == "-") return value

    val currencyDecimalDigits = if (decimalDigits != null) {
        decimalDigits
    } else {
        val currency = Currency.getInstance(locale)
        try {
            currency.defaultFractionDigits
        } catch (e: Exception) {
            e.printStackTrace()
            Currency.getInstance(defaultLocale).defaultFractionDigits
        }
    }

    val currencyFormatter: DecimalFormat = try {
        (DecimalFormat.getCurrencyInstance(locale) as DecimalFormat).apply {
            if (!showCurrencySymbol) {
                decimalFormatSymbols = decimalFormatSymbols.also {
                    it.currencySymbol = ""
                }
            }
        }
    } catch (e: Exception) {
        try {
            DecimalFormat.getCurrencyInstance(defaultLocale) as DecimalFormat
        } catch (e1: Exception) {
            DecimalFormat.getCurrencyInstance(Locale.US) as DecimalFormat
        }
    }


    //retain information about the negativity of the value before stripping all non-digits
    val isNegative = value.contains("-")

    val finalValue = value.let {
        //strip all non-digits so the formatter always has a 'clean slate' of numbers to work with
        it.replace("[^\\d]".toRegex(), "").takeIf {
            it.isNotEmpty()
        } ?: "0"
    }.let {
        //if we're given a value that's smaller than our decimal location, pad the value.
        val clearValue = it.takeUnless { it.length <= currencyDecimalDigits } ?: it.let {
            val formatString = "%" + currencyDecimalDigits + "s"
            String.format(formatString, it).replace(' ', '0')
        }

        //place the decimal in the proper location to construct a double which we will give the formatter.
        //This is NOT the decimal separator for the currency value, but for the double which drives it.
        val preparedVal =
            StringBuilder(clearValue).insert(clearValue.length - currencyDecimalDigits, '.')
                .toString()

        //Convert the string into a double, which will be passed into the currency formatter and recover the negative sign
        val newTextValue = preparedVal.toDouble() * (if (isNegative) -1 else 1).toDouble()

        //finally, do the actual formatting
        currencyFormatter.minimumFractionDigits = currencyDecimalDigits
        currencyFormatter.format(newTextValue)
    }

    return customFormatting?.invoke(
        finalValue,
        currencyFormatter.decimalFormatSymbols.currencySymbol
    ) ?: finalValue
}
