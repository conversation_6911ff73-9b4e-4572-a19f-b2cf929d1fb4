package id.co.bri.brimo.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Filter;
import android.widget.Filterable;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ItemListBankBinding;
import id.co.bri.brimo.models.apimodel.response.WilayahDataPbbResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dresta on 31/10/2022
 */
public class ListCityPbbAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements Filterable {
    private List<WilayahDataPbbResponse> list;
    private List<WilayahDataPbbResponse> listFiltered;
    private OnClickListener onClickListener;

    public interface OnClickListener {
        void onClickItem(WilayahDataPbbResponse WilayahDataPbbResponse);
    }

    public ListCityPbbAdapter(List<WilayahDataPbbResponse> list, OnClickListener onClickListener) {
        this.list = list;
        this.onClickListener = onClickListener;
        listFiltered = list;
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            @Override
            protected FilterResults performFiltering(CharSequence charSequence) {
                if (charSequence.toString().isEmpty())
                    listFiltered = list;
                else {
                    ArrayList<WilayahDataPbbResponse> listnew = new ArrayList<>();
                    for (WilayahDataPbbResponse b : list) {
                        if (b.getWilayahName().toLowerCase().contains(charSequence.toString().toLowerCase()))
                            listnew.add(b);
                    }
                    listFiltered = listnew;
                }
                FilterResults filterResults = new FilterResults();
                filterResults.values = listFiltered;
                return filterResults;
            }

            @Override
            protected void publishResults(CharSequence charSequence, FilterResults filterResults) {
                listFiltered = (List<WilayahDataPbbResponse>) filterResults.values;
                notifyDataSetChanged();
            }
        };
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ItemListBankBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        MyViewHolder myViewHolder = (MyViewHolder) holder;
        myViewHolder.binding.tvBank.setText(listFiltered.get(position).getWilayahName());
        myViewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onClickListener.onClickItem(listFiltered.get(position));
            }
        });
    }

    @Override
    public int getItemCount() {
        return (listFiltered != null) ? listFiltered.size() : 0;
    }

    protected static class MyViewHolder extends RecyclerView.ViewHolder {
        ItemListBankBinding binding;

        public MyViewHolder(ItemListBankBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
