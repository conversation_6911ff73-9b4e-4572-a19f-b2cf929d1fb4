package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class SetoranAwalResponse {
    @SerializedName("briva_number")
    @Expose
    private String brivaNumber;
    @SerializedName("deposit_amount")
    @Expose
    private String depositAmount;
    @SerializedName("expire_time")
    @Expose
    private Integer expireTime;
    @SerializedName("expire_date")
    @Expose
    private String expireDate;
    @SerializedName("response_id")
    @Expose
    private String responseId;
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("html_payment_method")
    @Expose
    private String htmlPaymentMethod;


    @SerializedName("description")
    @Expose
    private String description;


    public SetoranAwalResponse(String brivaNumber, String depositAmount, Integer expireTime,
                               String expireDate, String responseId, String name,
                               String accountNumber, String username, String referenceNumber,
                               String htmlPaymentMethod, String description) {
        this.brivaNumber = brivaNumber;
        this.depositAmount = depositAmount;
        this.expireTime = expireTime;
        this.expireDate = expireDate;
        this.responseId = responseId;
        this.name = name;
        this.accountNumber = accountNumber;
        this.username = username;
        this.referenceNumber = referenceNumber;
        this.htmlPaymentMethod = htmlPaymentMethod;
        this.description = description;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getBrivaNumber() {
        return brivaNumber;
    }

    public void setBrivaNumber(String brivaNumber) {
        this.brivaNumber = brivaNumber;
    }

    public String getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(String depositAmount) {
        this.depositAmount = depositAmount;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getHtmlPaymentMethod() {
        return htmlPaymentMethod;
    }

    public void setHtmlPaymentMethod(String htmlPaymentMethod) {
        this.htmlPaymentMethod = htmlPaymentMethod;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}