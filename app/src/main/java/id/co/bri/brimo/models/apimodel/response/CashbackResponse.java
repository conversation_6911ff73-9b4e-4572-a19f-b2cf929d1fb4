package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class CashbackResponse {
    @SerializedName("code")
    @Expose
    private String code;
    @SerializedName("title_string")
    @Expose
    private String titleString;
    @SerializedName("maksimum_string")
    @Expose
    private String maksimumString;
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("min_transaction")
    @Expose
    private Integer minTransaction;
    @SerializedName("due_date")
    @Expose
    private String dueDate;
    @SerializedName("due_date_string")
    @Expose
    private String dueDateString;
    @SerializedName("feature_main")
    @Expose
    private String featureMain;
    @SerializedName("feature_sub")
    @Expose
    private String featureSub;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("choose")
    @Expose
    private boolean choose;
    @SerializedName("data_detail")
    @Expose
    private List<DataView> cashbackDataView;
    @SerializedName("img_path")
    @Expose
    private String imgPath;
    @SerializedName("feature_id")
    @Expose
    private String featureId;

    public CashbackResponse(String code, String titleString, String maksimumString, String type, Integer minTransaction, String dueDate, String dueDateString,
                            String featureMain, String featureSub, String description, List<DataView> cashbackDataView, String imgPath) {
        this.code = code;
        this.titleString = titleString;
        this.maksimumString = maksimumString;
        this.type = type;
        this.minTransaction = minTransaction;
        this.dueDate = dueDate;
        this.dueDateString = dueDateString;
        this.featureMain = featureMain;
        this.featureSub = featureSub;
        this.description = description;
        this.cashbackDataView = cashbackDataView;
        this.imgPath = imgPath;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMinTransaction() {
        return minTransaction;
    }

    public void setMinTransaction(Integer minTransaction) {
        this.minTransaction = minTransaction;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getDueDateString() {
        return dueDateString;
    }

    public void setDueDateString(String dueDateString) {
        this.dueDateString = dueDateString;
    }

    public String getFeatureMain() {
        return featureMain;
    }

    public void setFeatureMain(String featureMain) {
        this.featureMain = featureMain;
    }

    public String getFeatureSub() {
        return featureSub;
    }

    public void setFeatureSub(String featureSub) {
        this.featureSub = featureSub;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isChoose() {
        return choose;
    }

    public void setChoose(boolean choose) {
        this.choose = choose;
    }

    public List<DataView> getCashbackDataView() {
        return cashbackDataView;
    }

    public void setCashbackDataView(List<DataView> cashbackDataView) {
        this.cashbackDataView = cashbackDataView;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getTitleString() {
        return titleString;
    }

    public void setTitleString(String titleString) {
        this.titleString = titleString;
    }

    public String getMaksimumString() {
        return maksimumString;
    }

    public void setMaksimumString(String maksimumString) {
        this.maksimumString = maksimumString;
    }

    public String getFeatureId() {
        return featureId;
    }

    public void setFeatureId(String featureId) {
        this.featureId = featureId;
    }
}