package id.co.bri.brimo.adapters.onboarding

import android.content.Context
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemTermCheckboxBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.TermCheckbox

class TermCheckboxAdapter(
    private val mContext: Context,
    private val termCheckboxs: MutableList<TermCheckbox> = mutableListOf(),
    private val checkboxClick: CheckboxClick
) : RecyclerView.Adapter<TermCheckboxAdapter.CheckboxHolder>() {

    private var lineOn: Boolean = false

    interface CheckboxClick {
        fun itemClickCheck(termCheckbox: TermCheckbox, position: Int, isCheck: Boolean)
    }

    inner class CheckboxHolder(val binding: ItemTermCheckboxBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CheckboxHolder {
        val binding =
            ItemTermCheckboxBinding.inflate(LayoutInflater.from(mContext), parent, false)
        return CheckboxHolder(binding)
    }

    override fun getItemCount(): Int {
        return if (termCheckboxs.isNotEmpty()) termCheckboxs.size else 0
    }

    override fun onBindViewHolder(holder: CheckboxHolder, position: Int) {
        holder.binding.apply {
            val spannedText = Html.fromHtml(termCheckboxs[position].value, Html.FROM_HTML_MODE_LEGACY)
            val highlightedText = highlightLinks(spannedText)
            tvSyarat.text = highlightedText
            tvSyarat.movementMethod = LinkMovementMethod.getInstance()

            if (termCheckboxs[position].absence != "M" && !lineOn) {
                lineOn = true
                line.visibility = View.VISIBLE
            }

            holder.itemView.setOnClickListener {
                cbSyarat.isChecked = !cbSyarat.isChecked
                checkboxClick.itemClickCheck(
                    termCheckboxs[position],
                    position,
                    cbSyarat.isChecked
                )
            }

            cbSyarat.setOnCheckedChangeListener { _, isChecked ->
                checkboxClick.itemClickCheck(termCheckboxs[position], position, isChecked)
            }
        }
    }

    private fun highlightLinks(spanned: Spanned): SpannableString {
        val spannableString = SpannableString(spanned)

        spanned.getSpans(0, spanned.length, android.text.style.URLSpan::class.java).forEach { span ->
            val start = spanned.getSpanStart(span)
            val end = spanned.getSpanEnd(span)
            val colorSpan = ForegroundColorSpan(GeneralHelper.getColor(R.color.blue_link))
            spannableString.setSpan(colorSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        return spannableString
    }
}