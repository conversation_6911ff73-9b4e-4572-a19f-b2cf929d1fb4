package id.co.bri.brimo.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentBottomBeliPulsaBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;


public class FragmentBeliPulsa extends BottomSheetDialogFragment {

    private FragmentBottomBeliPulsaBinding binding;
    private static String mHarga;

    public static FragmentBeliPulsa newInstance(String harga) {
        mHarga = harga;
        return new FragmentBeliPulsa();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {

        binding = FragmentBottomBeliPulsaBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.txtNominal.setText(String.format(getResources().getString(R.string.text_nominal), GeneralHelper.formatNominal((mHarga))));
    }
}
