package id.co.bri.brimo.ui.fragments;

import static org.chromium.base.ContextUtils.getApplicationContext;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.MimeTypeMap;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListDateMutationAdapter;
import id.co.bri.brimo.adapters.ListDownloadAdapter;
import id.co.bri.brimo.contract.IPresenter.mutasi.IDownloadMutationPresenter;
import id.co.bri.brimo.contract.IView.mutasi.IDownloadMutationView;
import id.co.bri.brimo.databinding.FragmentDownloadMutationBinding;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.EStatementDataModel;
import id.co.bri.brimo.models.EStatementListModel;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.FormRequestEStatementResponse;
import id.co.bri.brimo.models.apimodel.response.ListEStatementResponse;
import id.co.bri.brimo.ui.activities.EditEmailActivity;
import id.co.bri.brimo.ui.activities.LupaPinActivity;
import id.co.bri.brimo.ui.activities.RequestDownloadMutationActivity;
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment;

public class DownloadMutationFragment extends BaseFragment implements IDownloadMutationView, View.OnClickListener, ConfirmationDownloadMutationFragment.ConfirmationListener,
        SwipeRefreshLayout.OnRefreshListener, ListDownloadAdapter.DownloadInterface, CustomBottomDialogFragment.DialogDefaultListener, PinFragment.SendPin {

    private FragmentDownloadMutationBinding binding;

    private static final String TAG = "DownloadMutationFragment";

    protected ListDateMutationAdapter listDateMutationAdapter;
    protected ListDownloadAdapter listDownloadAdapter;
    private ArrayList<EStatementDataModel> eStatementDataModels = new ArrayList<>();
    private FormRequestEStatementResponse formRequestEStatement;

    private SkeletonScreen skeletonEStatement;

    private AmazonS3Client s3;
    private BasicAWSCredentials credentials;
    private ClientConfiguration configuration;
    private S3ClientOptions options;
    private Date expired;
    private GeneratePresignedUrlRequest generatePresignedUrl;
    private URL url;
    DownloadManager downloadManager;
    private long downloadID;
    private String fileName, urlString, name, cifString;

    CustomBottomDialogFragment customBottomDialogFragment;

    @Inject
    IDownloadMutationPresenter<IDownloadMutationView> presenter;

    public DownloadMutationFragment() {
    }

    public static DownloadMutationFragment newInstance() {
        DownloadMutationFragment fragment = new DownloadMutationFragment();
        Bundle args = new Bundle();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentDownloadMutationBinding.inflate(inflater, container, false);

        binding.btnReqEmpty.setOnClickListener(this);
        binding.btnReq.setOnClickListener(this);
        binding.srlEstatementMutation.setOnRefreshListener(this);

        injectDependency();

        skeletonEStatement = Skeleton.bind(binding.content)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_estatement)
                .show();

        handleRegisterReceiver();
        return binding.getRoot();
    }

    private void handleRegisterReceiver() {
        // Check API version before registering BroadcastReceiver
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requireContext().registerReceiver(onDownloadStatementComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_EXPORTED);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            requireContext().registerReceiver(onDownloadStatementComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_NOT_EXPORTED);
        } else {
            requireContext().registerReceiver(onDownloadStatementComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
        }
    }

    @Override
    public void onDestroyView() {
        binding = null;
        super.onDestroyView();
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
        }
    }

    @Override
    public void onSuccessGetListEStatement(ListEStatementResponse response) {
        boolean enableButton = true;
        binding.llEmptyState.setVisibility(View.GONE);
        binding.llEstatement.setVisibility(View.VISIBLE);
        binding.srlEstatementMutation.setRefreshing(false);
        for (int i = 0; i < response.geteStatementDataModels().size(); i++) {
            ArrayList<EStatementListModel> listEStatement = response.geteStatementDataModels().get(i).geteStatementListModels();
            for (int j = 0; j < listEStatement.size(); j++) {
                if (listEStatement.get(j).getStatus().equalsIgnoreCase("1")) {
                    enableButton = false;
                }
            }
        }

        if (enableButton) {
            binding.btnReq.setEnabled(true);
            binding.btnReq.setAlpha(1);
        } else {
            binding.btnReq.setEnabled(false);
            binding.btnReq.setAlpha(0.3f);
        }

        eStatementDataModels = response.geteStatementDataModels();
        binding.rvDate.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.VERTICAL, false));
        listDateMutationAdapter = new ListDateMutationAdapter(eStatementDataModels, listDownloadAdapter, getActivity(), this);
        binding.rvDate.setAdapter(listDateMutationAdapter);
        listDateMutationAdapter.notifyDataSetChanged();
    }

    @Override
    public void onSuccessGetFormRequestEStatement(FormRequestEStatementResponse response) {
        formRequestEStatement = response;
        if (formRequestEStatement.isFirstLoad()) {
            ConfirmationDownloadMutationFragment frg = new ConfirmationDownloadMutationFragment(getApplicationContext(), this, formRequestEStatement);
            frg.show(getActivity().getSupportFragmentManager(), "");
        } else {
            RequestDownloadMutationActivity.launchIntent(getActivity(), formRequestEStatement);
        }
    }

    @Override
    public void showEmptyState(EmptyStateResponse response) {
        binding.llEstatement.setVisibility(View.GONE);
        binding.llEmptyState.setVisibility(View.VISIBLE);
        binding.srlEstatementMutation.setRefreshing(false);
        binding.ivEmptyState.setImageResource(GeneralHelper.getImageId(getActivity(), response.getImageName()));
        binding.tvTitleEmpty.setText(response.getSubDescription());
        binding.btnReqEmpty.setEnabled(true);
    }

    @Override
    public void onException12(String message) {
        binding.srlEstatementMutation.setRefreshing(false);
        GeneralHelper.showSnackBar(requireActivity().findViewById(R.id.content), message);
    }

    @Override
    public void onException93(String message) {
        binding.srlEstatementMutation.setRefreshing(false);
        GeneralHelper.showSnackBar(requireActivity().findViewById(R.id.content), message);
    }

    @Override
    public void isHideSkeleton(boolean hide) {
        try {
            if (hide) {
                skeletonEStatement.hide();
            } else {
                skeletonEStatement.show();
            }
            binding.srlEstatementMutation.setRefreshing(false);
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "maxProtectionTempering: ", e);
            }
        }

    }

    @Override
    public void onSuccessEditEmail() {
        EditEmailActivity.launchIntent(getActivity());
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.btn_req_empty:
            case R.id.btn_req:
                getFormEStatement();
                break;
        }
    }

    private void getFormEStatement() {
        if (presenter != null) {
            presenter.setUrlForGetFormRequest(GeneralHelper.getString(R.string.url_v1_form_request_estatement));
            presenter.getFormRequestEStatement();
        }
    }

    public void getEStatementList() {
        if (presenter != null) {
            presenter.setUrlForGetList(GeneralHelper.getString(R.string.url_v1_get_list_estatement));
            presenter.getEStatementList();
        }
    }

    @Override
    public void onClickContinue(FormRequestEStatementResponse response) {
        RequestDownloadMutationActivity.launchIntent(getActivity(), formRequestEStatement);
    }

    @Override
    public void onClickChangeEmail() {
        PinFragment pinFragment = new PinFragment(requireActivity(), this);
        pinFragment.show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_DOWNLOAD_MUTATION) {
            if (resultCode == Activity.RESULT_OK) {
                getEStatementList();
            }
        }
    }

    @Override
    public void onRefresh() {
        getEStatementList();
    }

    @Override
    public void clickDownload(EStatementListModel eStatementListModel) {
        String myStr = eStatementListModel.getFileName();
        String strEnd = myStr.substring(myStr.lastIndexOf('/') + 1);
        cifString = myStr.substring(0, myStr.lastIndexOf('/'));

        url = downloadFile(myStr);
        urlString = url.toString();
        name = strEnd;

        fileName = url.getPath();
        if (!hasPermissions(getActivity(), PERMISSIONS)) {
            ActivityCompat.requestPermissions(getActivity(), PERMISSIONS, PERMISSIONS_ALL);
        } else {
            if (url != null) {
                callDownloadManager();
            }
        }
    }

    private URL downloadFile(String nameFile) {
        configuration = new ClientConfiguration();
        configuration.setMaxErrorRetry(3);
        configuration.setConnectionTimeout(501000);
        configuration.setSocketTimeout(501000);
        configuration.setProtocol(Protocol.HTTP);
        options = new S3ClientOptions();
        options.setPathStyleAccess(true);

        credentials = new BasicAWSCredentials(AppConfig.getAccessKeyMinio(), AppConfig.getSecretKeyMinio());
        s3 = new AmazonS3Client(credentials);
        s3.setRegion(Region.getRegion(AppConfig.REGION));
        s3.setEndpoint(AppConfig.getURLMinio());
        s3.setS3ClientOptions(options);

        expired = new Date(new Date().getTime() + 2000 * 60);
        generatePresignedUrl = new GeneratePresignedUrlRequest(AppConfig.getBucketEStatement(), nameFile);
        generatePresignedUrl.setMethod(HttpMethod.GET);
        generatePresignedUrl.setExpiration(expired);
        return s3.generatePresignedUrl(generatePresignedUrl);
    }

    private void callDownloadManager() {
        showProgress();
        downloadManager = (DownloadManager) getActivity().getSystemService(Context.DOWNLOAD_SERVICE);
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url + ""));
        request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI |
                DownloadManager.Request.NETWORK_MOBILE);
        request.setTitle(name);
        request.allowScanningByMediaScanner();
        request.setAllowedOverMetered(true);
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, name);
        request.setMimeType(getMimeType(urlString));
        downloadID = downloadManager.enqueue(request);
    }

    public static String getMimeType(String url) {
        String type = null;
        String extension = MimeTypeMap.getFileExtensionFromUrl(url);
        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
        }
        return type;
    }

    private BroadcastReceiver onDownloadStatementComplete = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            long id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);

            if (downloadID == id) {
                hideProgress();
                validDownload(getContext(), downloadID);
            }
        }
    };

    public void validDownload(Context context, long downloadId) {
        DownloadManager dm = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
        if (dm != null) {
            try (Cursor c = dm.query(new DownloadManager.Query().setFilterById(downloadId))) {
                if (c.moveToFirst()) {
                    @SuppressLint("Range") int status = c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS));
                    if (status == DownloadManager.STATUS_SUCCESSFUL) {
                        customBottomDialogFragment = new CustomBottomDialogFragment(getActivity(), GeneralHelper.getString(R.string.success_download_description),
                                GeneralHelper.getString(R.string.success_download_sub_description),
                                GeneralHelper.getString(R.string.mutation_text_download_button_fragment), "img_verification_email", false, this::onClickDialog);
                        customBottomDialogFragment.show(getFragmentManager(), "");
                        openPreviewFile();
                    } else if (status == DownloadManager.STATUS_FAILED) {
                        customBottomDialogFragment = new CustomBottomDialogFragment(getActivity(), GeneralHelper.getString(R.string.failed_download_description),
                                GeneralHelper.getString(R.string.failed_download_sub_description),
                                GeneralHelper.getString(R.string.mutation_text_download_button_fragment), "img_verification_email_failed", false, this::onClickDialog);
                        customBottomDialogFragment.show(getFragmentManager(), "");
                    }
                }
            }
        }
    }

    private void openPreviewFile() {
        File file = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/" + name);
        if (file.canRead()) {
            Uri uri = FileProvider.getUriForFile(requireContext(), "id.co.bri.brimo" + ".provider", file);
            Intent i = new Intent(Intent.ACTION_VIEW);
            i.setDataAndType(uri, "application/pdf");
            i.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(i);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        presenter.stop();
        requireContext().unregisterReceiver(onDownloadStatementComplete);
    }

    @Override
    public void onClickDialog() {

    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(getActivity(), message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    public void onSendPinComplete(String pin) {
        if (presenter != null) {
            presenter.setUrlEditEmail(GeneralHelper.getString(R.string.url_edit_email_pin));
            presenter.onLoadEditEmail(pin);
        }
    }

    @Override
    public void onLupaPin() {
        LupaPinActivity.launchIntent(getActivity());
    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(getActivity(),
                Constant.IMAGE_SERVER_UNDER_MAINTENANCE,
                response.getDescription(), response.getSubDescription(),
                "img_flag_off", false, null, GeneralHelper.getString(R.string.mutation_text_download_button_fragment)
        );
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getFragmentManager(), "");
    }
}