package id.co.bri.brimo.ui.activities.pengelolaankartu

import android.app.Activity
import android.content.Intent
import android.graphics.PorterDuff
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.pengelolaankartu.ListItemBukaRekeningValasAdapter
import id.co.bri.brimo.adapters.pengelolaankartu.ListItemInfoRekeningAdapter
import id.co.bri.brimo.adapters.pengelolaankartu.ListItemRekeningValasAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.CardSettingVDCAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.ClickAction
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IDetailKelolaKartuPresenter
import id.co.bri.brimo.contract.IView.pengelolaankartu.IDetailKelolaKartuView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDetailKelolaKartuNewBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetBukaRekeningValasBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetCardBlockBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetListRekValasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.enums.CardSettingsVDCEnum
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.models.apimodel.request.BlokirRequest
import id.co.bri.brimo.models.apimodel.request.blockcard.BlockCardRequest
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.AccountBindingReq
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.EnableDisableTransactionRequest
import id.co.bri.brimo.models.apimodel.response.EmptyMutationResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.BindingNewAccountResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.InitChangePinResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.TransactionCardModel
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.SettingVirtualCardData
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity.Companion.launchIntentTabungan
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogGeneral
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogType
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom.DialogContinueDefaultListener
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import javax.inject.Inject


class DetailKelolaKartuNewActivity : BaseActivity(), IDetailKelolaKartuView,
    DialogContinueDefaultListener, PinFragment.SendPin, DialogInformation.OnActionClick {

    @Inject
    lateinit var presenter: IDetailKelolaKartuPresenter<IDetailKelolaKartuView>

    private lateinit var binding: ActivityDetailKelolaKartuNewBinding
    private val settingCardAdapter = CardSettingVDCAdapter()
    private lateinit var selectedCardSetting: SettingVirtualCardData
    private var fromPage: String = ""
    private var accountNumberBinding = ""
    private var isSwitchStatus = false
    private var isClickStatusKartu = false
    private var isTransactionStatusClick = false
    private var isClickAccountBinding = false
    private var isShowHideCardNumber = false
    private var isBlockedCard = false
    private var listItemBukaRekeningValasAdapter: ListItemBukaRekeningValasAdapter? = null
    private var listItemRekeningValasAdapter: ListItemRekeningValasAdapter? = null
    private var listItemInfoRekeningAdapter: ListItemInfoRekeningAdapter? = null
    private val brimoPrefRepository = BRImoPrefRepository(this)

    private val changeDebitPinLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){ result ->
        when(result.resultCode) {
            Constant.REQ_PIN_CHANGE_SUCCESS -> {
                val data = result.data?.getStringExtra(ChangePINDebitActivity.CHANGE_PIN_MESSAGE)
                GeneralHelper.showSnackBarGreen(binding.content, data)
                binding.scrollView.scrollTo(0, 0)
            }
            Constant.REQ_PIN_CHANGE_MAX_RETRY -> {
                setResult(result.resultCode, result.data)
                finish()
            }
        }
    }

    companion object {
        private val TAG = "DetailKelolaKartuActivity"
        const val EXTRA_BLOCK_CARD_CODE = "block-card-code"
        const val EXTRA_BLOCK_CARD_MESSAGE = "block-card-message"

        private var detailResponse: DetailKelolaKartuRes? = null
        private var sAccount: String? = null
        private var indexList: Int = 0

        fun launchIntent(
            caller: Activity,
            detailKelolaKartuRes: DetailKelolaKartuRes?,
            account: String?,
            position: Int
        ) {
            val intent = Intent(caller, DetailKelolaKartuNewActivity::class.java)
            detailResponse = detailKelolaKartuRes
            sAccount = account
            indexList = position
            caller.startActivityForResult(intent, Constant.REQ_CARD)
        }

        fun launchIntentWithReturn(
            caller: Activity,
            detailKelolaKartuRes: DetailKelolaKartuRes?,
            account: String?,
            position: Int
        ): Intent {
            val intent = Intent(caller, DetailKelolaKartuNewActivity::class.java)
            detailResponse = detailKelolaKartuRes
            sAccount = account
            indexList = position
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityDetailKelolaKartuNewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupToolbar() {
        GeneralHelper.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.info_detail_kelola_toolbar)
        )
        setStatusColor(R.color.toolbar_blue)
    }

    private fun setupView() {
        if (detailResponse != null) {
            binding.apply {
                setViewHeaderCard()
                setViewInfoRekening()
                setViewOpenValas()
                setViewConnectRek()
                setViewSettingCard(detailResponse?.cardStatusTransaction.orEmpty())
                enableButtonCopy()

                checkingCardIssue()
                checkingDCMSupport()
            }
        }
    }

    private fun setViewHeaderCard() {
        binding.apply {
            GeneralHelper.loadImageUrl(
                this@DetailKelolaKartuNewActivity, detailResponse?.cardImageUrl,
                ivCardDetail,
                R.drawable.kelola_kartu_default,
                0
            )
            tvCardName.text = detailResponse?.cardLabel
            listItemInfoRekeningAdapter =
                detailResponse?.accountNumberDetail?.let {
                    ListItemInfoRekeningAdapter(
                        this@DetailKelolaKartuNewActivity,
                        it
                    )
                }

            swipeRefresh.apply {
                setOnRefreshListener { getDetailCard() }
                isRefreshing = false
            }
        }
    }

    private fun setViewInfoRekening() {
        binding.apply {
            viewInfoRekeningKartu.tvCardNumber.text = detailResponse?.cardNumberMasked
            viewInfoRekeningKartu.rvInfoRekening.adapter = listItemInfoRekeningAdapter
        }
    }

    private fun setViewOpenValas() {
        binding.apply {
            viewOpenRekValas.tvTitle.text = detailResponse?.openAccountBox?.title
            viewOpenRekValas.tvDesc.text = detailResponse?.openAccountBox?.desc
            lyCardOpenValas.setOnClickListener {
                bottomSheetOpenValas(detailResponse?.openAccountBox?.infoList.orEmpty())
            }
            viewOpenRekValas.btnOpenValas.setOnClickListener {
                launchIntentTabungan(this@DetailKelolaKartuNewActivity, Constant.OPEN_ACCOUNT_VALAS)
            }
        }
    }

    private fun setViewConnectRek() {
        binding.apply {
            connectRekCardView.tvTitle.visibility = View.GONE
            connectRekCardView.clCardNumber.visibility = View.GONE
            connectRekCardView.view.visibility = View.GONE
            connectRekCardView.rvInfoRekening.adapter = listItemInfoRekeningAdapter
            if (detailResponse?.cardIssue == false) {
                layoutUbah.isEnabled = true
                layoutUbah.alpha = 1F
                tvDetailRekening.setTextColor(GeneralHelper.getColor(R.color.primaryBlue80))
                ivPanah.setColorFilter(
                    resources.getColor(R.color.primaryBlue80),
                    PorterDuff.Mode.SRC_IN
                )
                layoutUbah.setOnClickListener {
                    bottomSheetListRekValas(detailResponse?.dynamicCurrencyAccountOption.orEmpty())
                }
            } else {
                layoutUbah.alpha = 0.3F
                tvDetailRekening.setTextColor(GeneralHelper.getColor(R.color.neutralLight40))
                ivPanah.setColorFilter(
                    resources.getColor(R.color.neutralLight40),
                    PorterDuff.Mode.SRC_IN
                )
                layoutUbah.isEnabled = false
            }
        }
    }

    private fun setViewSettingCard(listSetting: List<TransactionCardModel>) {
        detailResponse?.let { settingCardAdapter.managementCardDetail = it }
        binding.settingCardView.rvSettingCard.apply {
            visibility = View.VISIBLE
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = settingCardAdapter
        }

        var listSettingDetail: ArrayList<SettingVirtualCardData> = arrayListOf()
        var statusKartu = SettingVirtualCardData(
            detailResponse?.cardStatus ?: false,
            Constant.CARD_STATUS,
            GeneralHelper.getString(R.string.status_kartu),
            isTextSwitchOn(detailResponse?.cardStatus ?: false),
            CardSettingsVDCEnum.VIEW_TYPE_SWITCH.value,
            detailResponse?.cardIssue ?: false
        )
        listSettingDetail.add(statusKartu)
        listSetting.forEach { setting ->
            val description = isTextSwitchOn(setting.isStatus)
            listSettingDetail.add(
                SettingVirtualCardData(
                    setting.isStatus,
                    setting.type,
                    setting.title,
                    description,
                    CardSettingsVDCEnum.VIEW_TYPE_SWITCH.value,
                    detailResponse?.cardIssue ?: false
                )
            )
        }

        if (detailResponse?.showLimitSettingButton == true) {
            listSettingDetail.add(
                SettingVirtualCardData(
                    detailResponse?.showLimitSettingButton ?: false,
                    "LIMIT",
                    GeneralHelper.getString(R.string.atur_limit_kartu),
                    "",
                    CardSettingsVDCEnum.VIEW_TYPE_ACTION.value,
                    detailResponse?.cardIssue ?: false
                ),
            )
        }

        listSettingDetail.add(
            SettingVirtualCardData(
                detailResponse?.isEnabledChangePin ?: false,
                CardSettingVDCAdapter.CHANGE_PIN_ACTION_TYPE,
                GeneralHelper.getString(R.string.change_pin_debit_card_label),
                "",
                CardSettingsVDCEnum.VIEW_TYPE_ACTION.value,
                detailResponse?.cardIssue
                    ?: false
            ),
        )

        settingCardAdapter.settingVirtualCardList = listSettingDetail
        settingCardAdapter.setOnClickListener { action ->
            when (action) {
                is ClickAction.Transaction -> {
                    selectedCardSetting = action.setting
                    when {
                        action.setting.type.contains(Constant.CARD_STATUS) -> {
                            isSwitchStatus = action.setting.status
                            if (detailResponse?.cardIssue == false) {
                                isClickStatusKartu = true
                                settingStatusCard(!isSwitchStatus)
                            }
                        }
                        else -> {
                            isSwitchStatus = !action.setting.status
                            isTransactionStatusClick = true
                            settingStatusTrx(isSwitchStatus)
                        }
                    }
                }

                is ClickAction.Action -> {
                    when {
                        detailResponse?.cardIssue == false && action.action == GeneralHelper.getString(R.string.atur_limit_kartu) -> {
                            ListCardTypesActivity.launchIntent(this, sAccount)
                        }
                        detailResponse?.enableBlockCard == true && action.action == GeneralHelper.getString(R.string.action_block_card_permanently) -> {
                            showBottomDialogBlockCard()
                        }
                    }
                }

                is ClickAction.ChangePin -> {
                    if (detailResponse?.isEnabledChangePin == true) {
                        detailResponse?.cardNumber?.let { cardNumber ->
                            presenter.getChangePinRefNum(
                                getString(R.string.url_card_management_init_change_pin),
                                cardNumber
                            )
                        }
                    }
                }
            }
        }

        handleBlockCard(listSettingDetail)

        settingCardAdapter.notifyDataSetChanged()
    }

    private fun handleBlockCard(settingList: ArrayList<SettingVirtualCardData>) {

        if (detailResponse?.enableBlockCard == null) return

        // auto scroll to block card menu
        if (!brimoPrefRepository.blockCardBubble) {
            with(binding.scrollView) { post { fullScroll(View.FOCUS_DOWN) } }
        }

        if (!isBlockedCard) {
            settingList.add(
                SettingVirtualCardData(
                    status = detailResponse?.enableBlockCard ?: false,
                    type = Constant.BLOCK,
                    title = GeneralHelper.getString(R.string.action_block_card_permanently),
                    subTitle = "",
                    style = CardSettingsVDCEnum.VIEW_TYPE_ACTION_BLOCK.value,
                    cardIssue = detailResponse?.cardIssue ?: false
                ),
            )
        }
    }

    private fun showBottomDialogBlockCard() {
        val bottomSheetDialogGeneral = BottomSheetDialogGeneral.Builder()
            .setType(BottomSheetDialogType.TWO_VERTICAL_BUTTONS)
            .setImageResId(R.drawable.ic_transaksi_gagal)
            .setTitle(GeneralHelper.getString(R.string.txt_title_block_card))
            .setDescription(GeneralHelper.getString(R.string.txt_desc_block_card))
            .setPositiveText(GeneralHelper.getString(R.string.txt_yes_block_card))
            .setConfirmButtonVerticalStyle(R.style.CustomButtonPrimaryStyle, Typeface.BOLD)
            .setConfirmButtonVerticalTextColor(R.color.whiteColor)
            .setOnPositiveButtonClickListener {
                showInputPin()
                isBlockedCard = true
            }
            .setNegativeText(GeneralHelper.getString(R.string.batal))
            .setCancelButtonVerticalStyle(R.style.CustomButtonPrimaryOutlineStyle, Typeface.BOLD)
            .setCancelButtonVerticalTextColor(R.color.primary_blue80)
            .setOnNegativeButtonClickListener {
                if (!supportFragmentManager.isStateSaved || supportFragmentManager.findFragmentByTag("")?.isAdded == true) {
                    isBlockedCard = false
                    it.dismiss()
                }
            }
            .setCancelable(true)
            .setOnCanceledOnTouchOutside(true)
            .setOnCollapseDialog {
                it.dismiss()
            }
            .build()

        if (!supportFragmentManager.isStateSaved) {
            bottomSheetDialogGeneral.show(supportFragmentManager, bottomSheetDialogGeneral.tag)
        }
    }

    private fun isTextSwitchOn(status: Boolean): String {
        return if (status) GeneralHelper.getString(R.string.aktif) else GeneralHelper.getString(R.string.tidak_aktif)
    }

    private fun settingStatusCard(isStatusCard: Boolean) {
        if (isStatusCard) {
            if (presenter != null) {
                presenter.setUrlStatusCard(GeneralHelper.getString(R.string.url_unblok_kartu))
            }
            dialogTrx(
                GeneralHelper.getString(R.string.title_dialog_aktifkan_kartu),
                GeneralHelper.getString(R.string.desc_dialog_aktifkan_kartu),
                GeneralHelper.getString(R.string.aktifkan2)
            )
        } else {
            if (presenter != null) {
                presenter.setUrlStatusCard(GeneralHelper.getString(R.string.url_blok_kartu))
            }
            dialogTrx(
                GeneralHelper.getString(R.string.title_dialog_nonaktifkan_kartu),
                GeneralHelper.getString(R.string.desc_dialog_nonaktifkan_kartu),
                GeneralHelper.getString(R.string.nonaktifkan)
            )
        }
    }

    private fun settingStatusTrx(isStatusTrx: Boolean) {
        val title: String
        val subTitle: String
        val btnCont: String
        if (isStatusTrx) {
            title = resources.getString(
                R.string.non_active_transaction_status_card_title_dialog,
                selectedCardSetting.title
            )
            subTitle = resources.getString(
                R.string.non_active_transaction_status_card_subtitle_dialog,
                selectedCardSetting.title
            )
            btnCont = GeneralHelper.getString(R.string.nonaktifkan)
        } else {
            title = resources.getString(
                R.string.active_transaction_status_card_title_dialog,
                selectedCardSetting.title
            )
            subTitle = resources.getString(
                R.string.active_transaction_status_card_subtitle_dialog,
                selectedCardSetting.title
            )
            btnCont = GeneralHelper.getString(R.string.aktifkan_2)
        }

        dialogTrx(title, subTitle, btnCont)
    }

    private fun dialogTrx(title: String, subTitle: String, sBtnYes: String) {
        val dialogContinueCustom = DialogContinueCustom(
            this, title, subTitle,
            sBtnYes, GeneralHelper.getString(R.string.batal2), false
        )
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogContinueCustom, null)
        ft.commitAllowingStateLoss()
    }

    override fun onClickContinueYes() {
        showInputPin()
    }

    override fun onClickContinueNo() {
        isClickStatusKartu = false
        isTransactionStatusClick = false
    }

    override fun onException(message: String?) {
        super.onException(message)
        binding.scrollView.scrollTo(0, 0)
    }

    private fun showInputPin() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }

    private fun checkingCardIssue() {
        binding.apply {
            if (detailResponse?.cardIssue == true) {
                if (detailResponse?.issueType.equals("BL", ignoreCase = true) ||
                    detailResponse?.issueType.equals("CL", ignoreCase = true)
                ) {
                    cardBlockView.root.visibility = View.VISIBLE
                    cardBlockView.btnOpenValas.visibility = View.GONE
                    cardBlockView.tvTitle.text =
                        GeneralHelper.getString(R.string.title_kartu_terblokir)
                    cardBlockView.tvDesc.text =
                        GeneralHelper.getString(R.string.information_open_blokir)
                } else if (detailResponse?.issueType.equals("PS", ignoreCase = true)) {
                    cardBlockView.root.visibility = View.VISIBLE
                    cardBlockView.btnOpenValas.visibility = View.VISIBLE
                    cardBlockView.btnOpenValas.setOnClickListener {
                        bottomSheetCardBlock(GeneralHelper.getString(R.string.text_kartu_terblokir))
                    }
                }
            } else {
                cardBlockView.root.visibility = View.GONE
            }
        }
    }

    private fun checkingDCMSupport() {
        binding.apply {
            if (detailResponse?.dynamicCurrencySupported == true) {
                viewInfoRekeningKartu.root.visibility = View.VISIBLE
                clConnectRek.visibility = View.VISIBLE
                viewInfoRekeningKartu.lyInfoRek.visibility = View.GONE
                clCardOpenValas.visibility = View.GONE
                if (detailResponse?.dynamicCurrencyStatus == false) {
                    clConnectRek.visibility = View.GONE
                    viewInfoRekeningKartu.lyInfoRek.visibility = View.VISIBLE
                    clCardOpenValas.visibility = View.VISIBLE
                }
            } else {
                viewInfoRekeningKartu.root.visibility = View.VISIBLE
                clConnectRek.visibility = View.GONE
                viewInfoRekeningKartu.lyInfoRek.visibility = View.VISIBLE
                clCardOpenValas.visibility = View.GONE
            }
        }
    }

    private fun bottomSheetListRekValas(listValas: List<DetailKelolaKartuRes.DynamicCurencyAccountOption>) {
        val viewBind = FragmentBottomSheetListRekValasBinding.inflate(
            LayoutInflater.from(this)
        )
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {
            //do nothing
        }

        listItemRekeningValasAdapter =
            ListItemRekeningValasAdapter(this, listValas,
                object : ListItemRekeningValasAdapter.onClickItem {
                    override fun onClickItem(item: DetailKelolaKartuRes.DynamicCurencyAccountOption) {
                        bottomSheet.dismissNow()
                        isClickAccountBinding = true
                        accountNumberBinding = item.accountNumber
                        showInputPin()
                    }
                })
        viewBind.rvListValas.adapter = listItemRekeningValasAdapter

        viewBind.confirmBtn.setOnClickListener {
            bottomSheet.dismissNow()
            launchIntentTabungan(this@DetailKelolaKartuNewActivity, Constant.OPEN_ACCOUNT_VALAS)
        }

        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
    }

    private fun bottomSheetOpenValas(listInfoValas: List<DetailKelolaKartuRes.OpenAccountBox.InfoList>) {
        val viewBind = FragmentBottomSheetBukaRekeningValasBinding.inflate(
            LayoutInflater.from(this)
        )
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {
            //do nothing
        }
        listItemBukaRekeningValasAdapter =
            ListItemBukaRekeningValasAdapter(this, listInfoValas)

        viewBind.recyclerView.adapter = listItemBukaRekeningValasAdapter

        viewBind.confirmBtn.setOnClickListener {
            launchIntentTabungan(this, Constant.OPEN_ACCOUNT_VALAS)
            bottomSheet.dismissNow()
        }

        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
    }

    private fun bottomSheetCardBlock(desc: String) {
        val viewBind = FragmentBottomSheetCardBlockBinding.inflate(
            LayoutInflater.from(this)
        )
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {
            //do nothing
        }

        viewBind.tvDesc.text = desc

        viewBind.confirmBtn.setOnClickListener {
            InputNomorKtpActivity.launchIntent(
                this@DetailKelolaKartuNewActivity,
                detailResponse?.cardNumber
            )
            bottomSheet.dismissNow()
        }

        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
    }


    override fun onSendPinComplete(pin: String) {
        if (isClickStatusKartu) {
            presenter.setBlokirKartu(BlokirRequest(sAccount, pin))
            isClickStatusKartu = false
        }

        if (isTransactionStatusClick) {
            val request = EnableDisableTransactionRequest(
                selectedCardSetting.type,
                isSwitchStatus, sAccount, pin
            )
            presenter.setUrlTransactionStatus(GeneralHelper.getString(R.string.url_card_enable_disable_transaction))
            presenter.enableDisableTransactionStatus(request)
            isTransactionStatusClick = false
        }

        if (isClickAccountBinding) {
            presenter.setUrlAccountBinding(GeneralHelper.getString(R.string.url_account_binding))
            presenter.setAccountBinding(
                AccountBindingReq(
                    detailResponse?.cardNumber,
                    detailResponse?.accountNumber, accountNumberBinding, pin
                )
            )
            isClickAccountBinding = false
            accountNumberBinding = ""
        }

        if (isBlockedCard) {
            presenter.apply {
                setUrlBlockCard(GeneralHelper.getString(R.string.url_block_card))
                setBlockCard(BlockCardRequest(sAccount.orEmpty(), pin))
            }
        }
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this)
    }

    private fun getDetailCard() {
        binding.scrollView.scrollTo(0, 0)
        val request = DetailKelolaKartuReq(detailResponse?.cardNumber, sAccount)
        presenter.setUrlDetailCard(GeneralHelper.getString(R.string.url_card_management_card_detail_v2))
        presenter.getDataDetailCard(request)
    }

    override fun onSuccessGetStatusCard(desc: String?) {
        val description = getString(
            if (isSwitchStatus) R.string.success_activation_card
            else R.string.success_deactivation_card
        )
        showSnackbarErrorMessage(description, ALERT_CONFIRM, this, false)
        getDetailCard()
    }

    override fun onSuccessAccountBinding(response: BindingNewAccountResponse) {
        showSnackbarErrorMessage(
            getString(R.string.success_binding_account), ALERT_CONFIRM, this, false
        )
        sAccount = response.newAccountNumber
        getDetailCard()
    }

    override fun onSuccessGetDetailCard(response: DetailKelolaKartuRes?) {
        try {
            detailResponse = response
            setupView()
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onSuccessGetDetailCard: ", e)
            }
        } finally {
            binding.swipeRefresh.isRefreshing = false
        }
    }

    override fun onSuccessOrFailedBlockCard(code: String?, message: String?) {
        val intent = Intent().apply {
            putExtra(EXTRA_BLOCK_CARD_CODE, code.orEmpty())
            putExtra(EXTRA_BLOCK_CARD_MESSAGE, message.orEmpty())
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onSuccessEnableDisableTransaction(response: EmptyMutationResponse?) {
        val fragmentBottomDialog = FragmentBottomDialog(
            this,
            Constant.COMING_SOON,
            response?.description,
            response?.subDescription,
            response?.imageName, false,
            { getDetailCard() }, GeneralHelper.getString(R.string.ok)
        )
        fragmentBottomDialog.isCancelable = false
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    private fun enableButtonCopy() {
        binding.viewInfoRekeningKartu.llCopy.isClickable = true
        binding.viewInfoRekeningKartu.llCopy.isFocusable = true
        binding.viewInfoRekeningKartu.llCopy.setBackgroundResource(R.drawable.bg_primary_blue_10)
        binding.viewInfoRekeningKartu.tvCopy.setTextColor(getColor(R.color.blue_BRI80))
        val primaryBlue80 = GeneralHelper.getColor(R.color.blue_BRI80)
        binding.viewInfoRekeningKartu.ivCopy.setColorFilter(primaryBlue80)
        showHideCardNumber()
    }


    private fun showHideCardNumber() {
        binding.viewInfoRekeningKartu.llCopy.setOnClickListener {
            toggleShowHide()
        }
    }

    private fun toggleShowHide() {
        if (!isShowHideCardNumber) {
            isShowHideCardNumber = true
            showCardNumber()
        } else {
            isShowHideCardNumber = false
            hideCardNumber()
        }
    }

    private fun showCardNumber() {
        binding.apply {
            viewInfoRekeningKartu.ivCopy.setImageResource(R.drawable.ic_eye_hide)
            viewInfoRekeningKartu.ivCopy.setColorFilter(
                resources.getColor(
                    R.color.primaryBlue80,
                    theme
                ), PorterDuff.Mode.SRC_IN
            )
            viewInfoRekeningKartu.tvCopy.text = GeneralHelper.getString(R.string.btn_tutup)
            viewInfoRekeningKartu.tvCardNumber.text = detailResponse?.cardNumberString
        }
    }

    private fun hideCardNumber() {
        binding.apply {
            viewInfoRekeningKartu.ivCopy.setImageResource(R.drawable.ic_eye_unhide)
            viewInfoRekeningKartu.ivCopy.setColorFilter(
                resources.getColor(
                    R.color.primaryBlue80,
                    theme
                ), PorterDuff.Mode.SRC_IN
            )
            viewInfoRekeningKartu.tvCopy.text = GeneralHelper.getString(R.string.see)
            viewInfoRekeningKartu.tvCardNumber.text = detailResponse?.cardNumberMasked
        }
    }

    override fun onDestroy() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onDestroy()
        presenter.stop()
    }

    override fun onBackPressed() {
        val intent = Intent()
        intent.putExtra(Constant.TAG_TYPE, fromPage)
        if (fromPage == "fromSuccess") {
            setResult(Activity.RESULT_CANCELED, intent)
        } else {
            setResult(Activity.RESULT_OK, intent)
        }
        Handler().postDelayed({
            super.onBackPressed()
        }, 100)
    }

    override fun onClickAction() {}

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_CARD && resultCode == RESULT_OK && data != null) {
            GeneralHelper.showSnackBarGreen(
                findViewById(R.id.content),
                GeneralHelper.getString(R.string.pin_atm_berhasil_diubah)
            )
        } else if (requestCode == Constant.REQ_REISSUE && resultCode == RESULT_OK && data != null) {
            val dialog = DialogInformation(
                this,
                "iv_tutorial",
                GeneralHelper.getString(R.string.title_success_reissue),
                GeneralHelper.getString(R.string.desc_success_reissue),
                GeneralHelper.getString(R.string.ok), this,
                true, false, true, false
            )
            val ft = supportFragmentManager.beginTransaction()
            ft.add(dialog, null)
            ft.commitAllowingStateLoss()
        }
    }

    override fun onSuccessGetChangePinRefNum(refNum: String?, isBypass: Boolean) {
        detailResponse?.cardNumber?.let { cardNumber ->
            changeDebitPinLauncher.launch(
                ChangePINDebitActivity.launch(
                    context = this@DetailKelolaKartuNewActivity,
                    cardNumber = cardNumber,
                    refNum = refNum.toString(),
                    isByPassOldPin = isBypass,
                    cardType = CardType.DEBIT,
                )
            )
        }
    }
}