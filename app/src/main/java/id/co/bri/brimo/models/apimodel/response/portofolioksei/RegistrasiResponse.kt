package id.co.bri.brimo.models.apimodel.response.portofolioksei

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.response.BottomDrawer
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem

class RegistrasiResponse {

    @SerializedName("onboarding")
    val onboarding: Onboarding? = null

    @SerializedName("snk")
    val snk: String? = null

    @SerializedName("bottom_drawer")
    val bottomDrawer : BottomDrawer? =null

    class Onboarding{
        @SerializedName("image_path")
        val imgPath: String? = null

        @SerializedName("title")
        val title: String? = null

        @SerializedName("desc")
        val desc: String? = null
    }

    class BottomDrawer{
        @SerializedName("image_path")
        val imgPath: String? = null

        @SerializedName("title")
        val title: String? = null

        @SerializedName("desc")
        val desc: String? = null

        @SerializedName("button_text1")
        val buttonText1: String? = null

        @SerializedName("button_text2")
        val buttonText2: String? = null
    }
}