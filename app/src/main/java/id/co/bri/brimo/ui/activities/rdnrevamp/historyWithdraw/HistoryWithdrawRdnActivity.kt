package id.co.bri.brimo.ui.activities.rdnrevamp.historyWithdraw

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CustomFragmentPagerAdapter
import id.co.bri.brimo.databinding.ActivityHistoryWithdrawRdnBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.rdnrevamp.RdnWithdrawProcessFragment
import id.co.bri.brimo.ui.fragments.rdnrevamp.RdnWithdrawSuccessFragment

class HistoryWithdrawRdnActivity: BaseActivity(), ViewPager.OnPageChangeListener {

    private lateinit var binding: ActivityHistoryWithdrawRdnBinding
    private lateinit var viewAdapter: CustomFragmentPagerAdapter
    private lateinit var linearLayout: LinearLayout

    private var fragmentList = mutableListOf<Fragment>()
    private var titleTab = mutableListOf<String>()

    companion object {
        private const val RDN_ACCOUNT = "rdn_account"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            rdnAccount: String
        ) {
            val intent = Intent(caller, HistoryWithdrawRdnActivity::class.java)
            intent.putExtra(RDN_ACCOUNT, rdnAccount)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHistoryWithdrawRdnBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            }
        }
    }

    private fun setupView() {
        GeneralHelper.setToolbar(
            this,
            binding.tbCatatanKeuangan.toolbar,
            getString(R.string.title_mutasi)
        )

        titleTab.add(getString(R.string.txt_dalam_proses))
        titleTab.add(getString(R.string.txt_riwayat))

        val rdnAccount = intent.getStringExtra(RDN_ACCOUNT)

        fragmentList.add(RdnWithdrawProcessFragment.newInstance(rdnAccount.orEmpty()))
        fragmentList.add(RdnWithdrawSuccessFragment.newInstance(rdnAccount.orEmpty()))

        viewAdapter = CustomFragmentPagerAdapter(supportFragmentManager, fragmentList, titleTab)
        binding.vpCatatanKeuangan.adapter = viewAdapter
        binding.tabCatatanKeuangan.setViewPager(binding.vpCatatanKeuangan)

        linearLayout = binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout
        binding.tabCatatanKeuangan.setOnPageChangeListener(this)

        GeneralHelper.changeTabsFontBoldForMutation(this, linearLayout, 0)
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldForMutation(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }
}