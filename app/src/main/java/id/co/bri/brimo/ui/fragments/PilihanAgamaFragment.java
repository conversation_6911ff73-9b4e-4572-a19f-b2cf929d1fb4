package id.co.bri.brimo.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListPilihanAgamaAdapter;
import id.co.bri.brimo.databinding.FragmentPilihanAgamaBinding;
import id.co.bri.brimo.models.apimodel.response.ParameterAR;

/**
 * A simple {@link Fragment} subclass.
 */
public class PilihanAgamaFragment extends BottomSheetDialogFragment implements ListPilihanAgamaAdapter.OnCLickItemPilihan {

    private FragmentPilihanAgamaBinding binding;

    private ListPilihanAgamaAdapter agamaAdapter;
    private List<ParameterAR.Agama> dataList;

    SelectPilihan selectPilihan;

    public PilihanAgamaFragment(List<ParameterAR.Agama> dataList, SelectPilihan selectPilihan) {
        this.dataList = dataList;
        this.selectPilihan = selectPilihan;
    }

    public PilihanAgamaFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentPilihanAgamaBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.rvListAgama.setHasFixedSize(true);
        binding.rvListAgama.setLayoutManager(new LinearLayoutManager(getActivity()));
        agamaAdapter = new ListPilihanAgamaAdapter(this, dataList);
        binding.rvListAgama.setAdapter(agamaAdapter);
    }

    @Override
    public void onclick(String text) {
        dismiss();
        selectPilihan.onClickAgama(text);
    }

    public interface SelectPilihan {
        void onClickAgama(String text);
    }
}
