package id.co.bri.brimo.adapters.option;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ItemListPilihanDurasiBinding;
import id.co.bri.brimo.models.DurasiMutasiModel;

import java.util.List;

public class OptionTanggalMutasiAdapter extends RecyclerView.Adapter<OptionTanggalMutasiAdapter.ViewHolder> {

    private List<DurasiMutasiModel> durasiMutasiModels;
    private Context context;
    private ClickItemDurasi clickItem;

    public OptionTanggalMutasiAdapter(Context context, List<DurasiMutasiModel> durasiMutasiModels, ClickItemDurasi clickItem) {
        this.clickItem = clickItem;
        this.durasiMutasiModels = durasiMutasiModels;
        this.context = context;
    }

    @NonNull
    @Override
    public OptionTanggalMutasiAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemListPilihanDurasiBinding.inflate(LayoutInflater.from(context),
                parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull OptionTanggalMutasiAdapter.ViewHolder holder, int position) {
        DurasiMutasiModel durasiMutasiModel = durasiMutasiModels.get(position);
        OptionTanggalMutasiAdapter.ViewHolder myViewHolder = (OptionTanggalMutasiAdapter.ViewHolder) holder;

        myViewHolder.binding.llView.setOnClickListener(view -> {
            if (clickItem != null) {
                clickItem.onClickItem(position, durasiMutasiModel);
            }
        });

        myViewHolder.binding.tvPilihanDurasi.setText(durasiMutasiModel.getPilihTanggal());
    }

    @Override
    public int getItemCount() {
        return durasiMutasiModels.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemListPilihanDurasiBinding binding;

        public ViewHolder(@NonNull ItemListPilihanDurasiBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    public interface ClickItemDurasi {
        void onClickItem(int item, DurasiMutasiModel durasiMutasiModel);
    }
}
