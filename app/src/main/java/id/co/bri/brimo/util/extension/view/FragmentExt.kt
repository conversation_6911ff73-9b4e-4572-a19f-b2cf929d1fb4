package id.co.bri.brimo.util.extension.view

import androidx.fragment.app.FragmentManager

/**
 * Checks if a fragment with the given tag is currently added to this FragmentManager
 * and is visible on the screen.
 *
 * This is useful for determining if a specific fragment, identified by its unique tag,
 * is not only present in the fragment manager's back stack or list of added fragments
 * but is also currently visible to the user.
 *
 * @param tag The unique tag name of the fragment to check. This is the same tag
 *            that was used when the fragment was added or shown (e.g., via `FragmentTransaction.add(containerViewId, fragment, tag)`
 *            or `DialogFragment.show(fragmentManager, tag)`).
 * @return `true` if a fragment with the specified tag exists and its `isVisible` property is true,
 *         `false` otherwise (e.g., if the fragment is not found by the tag, or it exists but is not currently visible).
 */
fun FragmentManager.isFragmentShown(tag: String): Boolean {
    val existingFragment = findFragmentByTag(tag)
    return existingFragment != null && existingFragment.isVisible
}