package id.co.bri.brimo.contract.IPresenter.deposito;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.openDepoInquiryRequest;

public interface ITambahDaftarDepositoPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlInquiry(String urlInquiry);

    void setUrlKonfirmasi(String urlKonfirmasi);

    void setUrlPayment(String urlPayment);

    void sendDataInquiry(openDepoInquiryRequest request);
}
