package id.co.bri.brimo.adapters.applycc

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemApplyVccInfoStepBinding
import id.co.bri.brimo.models.applyccrevamp.ApplyCcSubmitDataModel
import id.co.bri.brimo.util.extension.loadImage
import id.co.bri.brimo.util.extension.visibleView
import id.co.bri.brimo.util.recyclerview.BaseRecyclerViewFilterAdapter

class ApplyVccInfoStepAdapter : BaseRecyclerViewFilterAdapter<ApplyCcSubmitDataModel.PendingApply.Progress>() {

    private var stepPositionIndex = 0
    private var statusTime = ""

    override fun onCreateView(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return ProductCcInfoStepAdapterHolder(ItemApplyVccInfoStepBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindView(data: ApplyCcSubmitDataModel.PendingApply.Progress, position: Int, holder: RecyclerView.ViewHolder) {
        when (holder) {
            is ProductCcInfoStepAdapterHolder -> holder.setData(data)
        }
    }

    inner class ProductCcInfoStepAdapterHolder(private val binding: ItemApplyVccInfoStepBinding) : RecyclerView.ViewHolder(binding.root) {
        fun setData(data: ApplyCcSubmitDataModel.PendingApply.Progress) = with(binding) {
            tvItemApplyCcInfoStepTitle.text = data.statusDesc
            tvItemApplyCcInfoStepTitle.setTextColor(
                ContextCompat.getColor(
                    root.context, when {
                        absoluteAdapterPosition <= stepPositionIndex -> R.color.primary_blue80
                        else -> R.color.neutral_light40
                    }
                )
            )

            tvItemApplyCcInfoStepDateTime.text = data.statusTime.ifEmpty { statusTime }
            tvItemApplyCcInfoStepDateTime.visibleView(data.current)

            tvItemApplyCcInfoStepCount.text = (absoluteAdapterPosition + 1).toString()
            tvItemApplyCcInfoStepCount.setTextColor(
                ContextCompat.getColor(
                    root.context, when {
                        absoluteAdapterPosition <= stepPositionIndex -> R.color.primary_blue80
                        else -> R.color.neutral_light40
                    }
                )
            )


            ivItemApplyCcInfoStepCount.loadImage(
                when {
                    absoluteAdapterPosition < stepPositionIndex || mainDatas.lastIndex == stepPositionIndex -> R.drawable.checkbox_on
                    absoluteAdapterPosition == stepPositionIndex -> R.drawable.circle_pin_blue80
                    else -> R.drawable.circle_pin_grey
                }
            )

            vItemApplyCcInfoStep.visibleView(mainDatas.lastIndex != absoluteAdapterPosition)
            vItemApplyCcInfoStep.background = ContextCompat.getDrawable(
                root.context, when {
                    absoluteAdapterPosition >= stepPositionIndex -> R.drawable.bg_line_neural40_gap_vertical
                    else -> R.drawable.bg_line_blue80_vertical
                }
            )
        }
    }

    fun setStepPosition(stepPosition: Int) {
        this.stepPositionIndex = stepPosition
    }

    fun setStatusTime(statusTime: String) {
        this.statusTime = statusTime
    }
}
