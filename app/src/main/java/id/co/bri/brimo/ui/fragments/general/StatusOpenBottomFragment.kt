package id.co.bri.brimo.ui.fragments.general

import androidx.fragment.app.FragmentManager

object StatusOpenBottomFragment {
    fun showDialogStatus(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true
    ) {
        val bottomSheet = StatusBottomFragment()
        bottomSheet.apply {
            setFieldType(StatusBottomFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            show(fragmentManager, "")
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isSubtitleTextHtml = false
        }
    }

    fun showDialogStatusWithSubtitleTextHtml(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true
    ) {
        val bottomSheet = StatusBottomFragment()
        bottomSheet.apply {
            setFieldType(StatusBottomFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            show(fragmentManager, "")
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isSubtitleTextHtml = true
        }
    }

    fun showDialogStatusWithoutImage(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true
    ) {
        val bottomSheet = StatusBottomFragment()
        bottomSheet.apply {
            setFieldType(StatusBottomFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            show(fragmentManager, "")
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            isWithoutImage = true
        }
    }
}