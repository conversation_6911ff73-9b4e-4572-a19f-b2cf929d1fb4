package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class CashbackFilterResponse {

    @SerializedName("data_redeem")
    @Expose
    private boolean dataRedeem;
    @SerializedName("list")
    @Expose
    private List<CashbackResponse> cashbackDetails;

    public CashbackFilterResponse(boolean dataRedeem, List<CashbackResponse> cashbackDetails) {
        this.dataRedeem = dataRedeem;
        this.cashbackDetails = cashbackDetails;
    }

    public boolean isDataRedeem() {
        return dataRedeem;
    }

    public void setDataRedeem(boolean dataRedeem) {
        this.dataRedeem = dataRedeem;
    }

    public List<CashbackResponse> getCashbackDetails() {
        return cashbackDetails;
    }

    public void setCashbackDetails(List<CashbackResponse> cashbackDetails) {
        this.cashbackDetails = cashbackDetails;
    }
}
