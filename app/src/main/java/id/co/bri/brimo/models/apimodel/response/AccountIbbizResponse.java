package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AccountIbbizResponse {
    @SerializedName("phone")
    @Expose
    private String phone;
    @SerializedName("email")
    @Expose
    private String email;
    @SerializedName("account_list")
    @Expose
    private List<AccountList> accountList;
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;

    public AccountIbbizResponse(String phone, String email, List<AccountList> accountList, String referenceNumber) {
        this.phone = phone;
        this.email = email;
        this.accountList = accountList;
        this.referenceNumber = referenceNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<AccountList> getAccountList() {
        return accountList;
    }

    public void setAccountList(List<AccountList> accountList) {
        this.accountList = accountList;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public class AccountList {
        @SerializedName("account")
        @Expose
        private String account;
        @SerializedName("account_string")
        @Expose
        private String accountString;
        @SerializedName("account_name")
        @Expose
        private String accountName;

        public AccountList(String account, String accountString, String accountName) {
            this.account = account;
            this.accountString = accountString;
            this.accountName = accountName;
        }

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public String getAccountString() {
            return accountString;
        }

        public void setAccountString(String accountString) {
            this.accountString = accountString;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }
    }
}
