package id.co.bri.brimo.contract.IView.cardless;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiTarikTunaiResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentSetorResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.SetorTunaiResponse;
import id.co.bri.brimo.models.apimodel.response.TarikTunaiResponse;

public interface IFormSetorTunaiView extends IMvpView{

    void onSuccessGetAccount(SetorTunaiResponse setorTunaiResponse);

    void onSuccessGetSetorTunai(PaymentSetorResponse paymentSetorResponse);

    void onTokenActive(PaymentSetorResponse paymentSetorResponse);

    int getDefaultIconResource();

    long getAmount();

    void onException12(String message);

    void onException93(String message);

    void showInputError(String message);

    void setDefaultSaldo(double defaultSaldo ,String stringSaldo, String akunDefault);

    int getLayoutResource();

    void setTextForm();

    String getTitleBar();
}
