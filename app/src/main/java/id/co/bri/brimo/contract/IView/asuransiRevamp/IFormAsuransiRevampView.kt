package id.co.bri.brimo.contract.IView.asuransiRevamp

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.FormAsuransiResponse

interface IFormAsuransiRevampView: IMvpView {

    fun onSuccessKonfirmasiAsuransi(response: InquiryBrivaRevampResponse)
}