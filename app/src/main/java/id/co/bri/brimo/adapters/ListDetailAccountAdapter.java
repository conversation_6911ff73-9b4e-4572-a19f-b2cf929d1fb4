package id.co.bri.brimo.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ListItemDetailAkunBripointBinding;
import id.co.bri.brimo.models.apimodel.response.DataView;
import io.rmiri.skeleton.master.AdapterSkeleton;

public class ListDetailAccountAdapter extends AdapterSkeleton<DataView, ListDetailAccountAdapter.MyViewHolder> {

    private List<DataView> dataView;
    private OnClickItem onClickItem;

    public interface OnClickItem {
        void onCallback();
    }

    public ListDetailAccountAdapter(Context context, List<DataView> dataViews, OnClickItem onClickItem) {
        this.dataView = dataViews;
        this.onClickItem = onClickItem;
    }

    @NonNull
    @Override
    public ListDetailAccountAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ListItemDetailAkunBripointBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ListDetailAccountAdapter.MyViewHolder holder, int position) {

        DataView dataViews = dataView.get(position);
        holder.binding.tvName.setText(dataViews.getName());
        holder.binding.tvValue.setText(dataViews.getValue());

        if (dataViews.getName().contains("Email"))
            holder.binding.ivEdit.setVisibility(View.VISIBLE);
        else
            holder.binding.ivEdit.setVisibility(View.GONE);

        holder.binding.ivEdit.setOnClickListener(v -> {
            onClickItem.onCallback();
        });
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        ListItemDetailAkunBripointBinding binding;

        public MyViewHolder(@NonNull ListItemDetailAkunBripointBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public int getItemCount() {
        return dataView.size();
    }
}

