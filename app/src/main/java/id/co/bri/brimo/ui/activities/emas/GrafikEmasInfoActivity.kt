package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IGrafikEmasInfoPresenter
import id.co.bri.brimo.contract.IView.emas.IGrafikEmasInfoView
import id.co.bri.brimo.databinding.ActivityGrafikEmasInfoBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.InvestasiConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.InfoResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogCase58
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode
import id.co.bri.brimo.ui.fragments.DialogFragmentRc02
import id.co.bri.brimo.ui.fragments.emas.BuyAndSellGoldFragment
import id.co.bri.brimo.ui.fragments.emas.GrafikBuyGoldFragment
import id.co.bri.brimo.ui.fragments.emas.GrafikSellGoldFragment
import java.util.*
import javax.inject.Inject

class GrafikEmasInfoActivity : BaseActivity(), BuyAndSellGoldFragment.DialogDefaultListener,
        IGrafikEmasInfoView,DialogFragmentRc02.DialogDefaultListener,BottomFragmentSafteyMode.DialogDefaulListener,
        DialogCase58.DialogDefaultListener, ViewPager.OnPageChangeListener, GrafikBuyGoldFragment.OnItemClickListener,
        GrafikSellGoldFragment.OnItemClickListener{
    lateinit var binding: ActivityGrafikEmasInfoBinding
    private var fragmentList: MutableList<Fragment>? = mutableListOf()
    private var adapterViewPager: CatatanKeuanganAdapter? = null

    //set Up Grafik
    var date : String? = null
    private var arrayTanggal : MutableList<String> = mutableListOf()
    private var xArray : List<String> = listOf()
    private var yArray : List<Int> = listOf()
    private var xStringArray : MutableList<String>? = mutableListOf()
    private var yStringArray : MutableList<Int>? = mutableListOf()
    private var xStringArray1month : MutableList<String>? = mutableListOf()
    private var yStringArray1month : MutableList<Int>? = mutableListOf()
    private var xStringArray3month : MutableList<String>? = mutableListOf()
    private var yStringArray3month : MutableList<Int>? = mutableListOf()
    private var xStringArray6month : MutableList<String>? = mutableListOf()
    private var yStringArray6month : MutableList<Int>? = mutableListOf()
    private var xStringArrayJual : MutableList<String>? = mutableListOf()
    private var yStringArrayJual : MutableList<Int>? = mutableListOf()
    private var xStringArray1monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray1monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArray3monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray3monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArray6monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray6monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArrayTemp : MutableList<String>? = mutableListOf()
    private var yStringArrayTemp : MutableList<Int>? = mutableListOf()
    private var index : Int? = null
    private var mBoolean = false
    var isTouchListenerEnabled = true
    private var maxNominal : Int? = null
    private var minNominal : Int? = null
    var dialog : DialogFragmentRc02? = null
    var dialog58 : DialogCase58? = null

    private var lyTabs: LinearLayout? = null
    private var titleList: MutableList<String>? = mutableListOf()


    @Inject
    lateinit var presenter : IGrafikEmasInfoPresenter<IGrafikEmasInfoView>

    companion object{
        private var mResponse: GrafikEmasResponse? = null
        lateinit var arrayBuyRateHistory: List<GrafikEmasResponse.BuyRateHistory>
        lateinit var arraySellRateHistory: List<GrafikEmasResponse.SellRateHistory>
        fun launchIntent(caller: Activity, response: GrafikEmasResponse) {
            val intent = Intent(caller, GrafikEmasInfoActivity::class.java)
            arrayBuyRateHistory = response.buyInfo!!.buyRateHistory!!
            arraySellRateHistory = response.sellInfo!!.sellRateHistory!!
            mResponse = response
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGrafikEmasInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependecy()
        setupView()
    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlFormJual(GeneralHelper.getString(R.string.url_jual_inquiry))
        presenter.setUrlGetFormBeli(GeneralHelper.getString(R.string.url_beli_inqury_grafik))
        presenter.setUrlPusatBantuan(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))

    }

    private fun setupView() {
        GeneralHelper.setToolbar(this, binding.tbGrafikEmas.toolbar, GeneralHelper.getString(R.string.tb_txt_grafik_info_emas))

        val buyFragment = GrafikBuyGoldFragment.newInstance(
            mResponse!!,
            binding.vpGrafikEmas,
            this
        )
        val sellFragment = GrafikSellGoldFragment.newInstance(
            mResponse!!,
            binding.vpGrafikEmas,
            this
        )
        fragmentList!!.add(buyFragment)
        fragmentList!!.add(sellFragment)
        titleList!!.add(GeneralHelper.getString(R.string.beli))
        titleList!!.add(GeneralHelper.getString(R.string.jual))

        adapterViewPager = CatatanKeuanganAdapter(supportFragmentManager,this, fragmentList!!,titleList)
        binding.vpGrafikEmas.adapter = adapterViewPager
        binding.tabGrafikEmasInfo.setViewPager(binding.vpGrafikEmas)

        binding.tabGrafikEmasInfo.setOnPageChangeListener(this)
        lyTabs = binding.tabGrafikEmasInfo.getChildAt(0) as LinearLayout

        GeneralHelper.changeTabsFontBoldRevamp(this, lyTabs, 0)

//        binding.tvLastUpdate.text = mResponse!!.lastUpdate!!.title

    }



    override fun onType(boolean: Boolean , mRepsonse: GrafikEmasResponse) {
        mBoolean = boolean
    }

    override fun onClickItem(boolean: Boolean) {
        if (boolean){
            presenter.getBeliEmas()
        }else{
            presenter.getJualEmas()
        }
    }



    override fun onBackPressed() {
        val i = Intent()
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onSuccessFormJualEmas(response: InquiryOpenEmasResponse) {
        FormJualEmasActivity.launchIntent(this,response)
    }

    override fun onSuccessFormBeliEmas(response: InquiryOpenEmasResponse) {
        InquiryBeliEmasActivity.launchIntent(this,response,false,false,InvestasiConfig.GoldType.GRAPHIC.status)
    }

    override fun exceptionEODEOM(response: onExceptionWH) {
        dialog = DialogFragmentRc02.newInstance(
                this,
                response
        )
        dialog!!.show(supportFragmentManager, "")
    }

    override fun onSafetyMode(response: SafetyModeDrawerResponse) {
        val bottomFragmentSafteyMode = BottomFragmentSafteyMode(
                this,
                response.drawerContent!!.title,
                response.drawerContent!!.description,
                "Saya Mengerti",
                "Pelajari Lebih Lanjut",
                Constant.OPEN_ACCOUNT_S3f
        )
        bottomFragmentSafteyMode.show(supportFragmentManager, "")
    }

    override fun onException58(response: InfoResponse) {
        dialog58 = DialogCase58.newInstance(
                this,
                response.title!!,
                response.desc!!,
                "Saya Mengerti"
        )
        dialog58!!.show(supportFragmentManager, "")
    }

    override fun onSuccessGetPusatBantuan(response: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, response, response.topicName);
    }

    override fun onClickDialogRc02() {
        dialog!!.dismiss()
    }

    override fun onClickToSafety() {
        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)    }

    override fun onClickDialogRc58() {
        dialog58!!.dismiss()

    }

    private fun setupDataGrafik(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray!!.clear()
            yStringArray!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray!!.add(string)
                }
            }
            val min = Collections.min(yStringArray)
            val max = Collections.max(yStringArray)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArrayJual!!.clear()
            yStringArrayJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArrayJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArrayJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArrayJual)
            val max = Collections.max(yStringArrayJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik1Month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray1month!!.clear()
            yStringArray1month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray1month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray1month!!.add(string)
                }
            }
            val min = Collections.min(yStringArray1month)
            val max = Collections.max(yStringArray1month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray1monthJual!!.clear()
            yStringArray1monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray1monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray1monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray1monthJual)
            val max = Collections.max(yStringArray1monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik3month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray3month!!.clear()
            yStringArray3month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray3month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray3month!!.add(string)
                }
            }
            val min = Collections.min(yStringArray3month)
            val max = Collections.max(yStringArray3month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray3monthJual!!.clear()
            yStringArray3monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray3monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray3monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray3monthJual)
            val max = Collections.max(yStringArray3monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik6month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){

        if (!fromDifferent){
            xStringArray6month!!.clear()
            yStringArray6month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size- range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray6month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray6month!!.add(string)
                }

            }
            val min = Collections.min(yStringArray6month)
            val max = Collections.max(yStringArray6month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray6monthJual!!.clear()
            yStringArray6monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size- range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray6monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray6monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray6monthJual)
            val max = Collections.max(yStringArray6monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldRevamp(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }

    override fun onBuyGold() {
        presenter.getBeliEmas()
    }

    override fun onSellGold() {
        presenter.getJualEmas()
    }


}