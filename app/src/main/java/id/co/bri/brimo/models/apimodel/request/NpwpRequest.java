package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class NpwpRequest {
    @SerializedName("verification_id")
    @Expose
    private String verifId;

    @SerializedName("npwp_skip")
    @Expose
    private Integer npwpSkip;

    @SerializedName("string_npwp")
    @Expose
    private String npwp;

    public NpwpRequest(String verifId, Integer npwpSkip, String npwp) {
        this.verifId = verifId;
        this.npwpSkip = npwpSkip;
        this.npwp = npwp;
    }

    public Integer getNpwpSkip() {
        return npwpSkip;
    }

    public void setNpwpSkip(Integer npwpSkip) {
        this.npwpSkip = npwpSkip;
    }

    public String getNpwp() {
        return npwp;
    }

    public void setNpwp(String npwp) {
        this.npwp = npwp;
    }

    public String getVerifId() {
        return verifId;
    }

    public void setVerifId(String verifId) {
        this.verifId = verifId;
    }
}
