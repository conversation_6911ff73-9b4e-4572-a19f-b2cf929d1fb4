package id.co.bri.brimo.ui.customviews.edittext;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.textfield.TextInputEditText;

public class NoCopyPasteEditText extends TextInputEditText {
    public NoCopyPasteEditText(@NonNull Context context) {
        super(context);
        init();
    }

    public NoCopyPasteEditText(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public NoCopyPasteEditText(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // Disable the long click context menu (copy/paste/cut) by default
        this.setLongClickable(false);
    }

    @Override
    public boolean onTextContextMenuItem(int id) {
        // Intercept the context menu to disable copy/paste/cut operations
        switch (id) {
            case android.R.id.cut:
            case android.R.id.paste:
            case android.R.id.copy:
                return false; // Return false to disable the action
            default:
                return super.onTextContextMenuItem(id);
        }
    }
}
