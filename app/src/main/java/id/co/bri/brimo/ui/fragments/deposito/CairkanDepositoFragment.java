package id.co.bri.brimo.ui.fragments.deposito;

import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.regex.Pattern;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentCairkanDepositoBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.ui.customviews.PatternEditableBuilder;

public class CairkanDepositoFragment extends BottomSheetDialogFragment {

    private FragmentCairkanDepositoBinding binding;

    private OnFragmentInteractionListener mListener;

    private String title;
    private String desc;
    private String tahun;
    private String bulan;
    private String hari;

    public CairkanDepositoFragment(String title, String desc,String tahun, String bulan, String hari, OnFragmentInteractionListener onClick) {
        this.title = title;
        this.desc = desc;
        this.tahun = tahun;
        this.bulan = bulan;
        this.hari = hari;
        this.mListener = onClick;
    }

    public interface OnFragmentInteractionListener {
        void onClickDeposito();
        void onCLickPenalti();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        binding = FragmentCairkanDepositoBinding.inflate(inflater, container, false);

        binding.tvTitle.setText(title);
        binding.tvDeskripsi.setText(Html.fromHtml(desc+" <b>Baca Detailnya</b>"));
        binding.tvHari.setText(hari);
        binding.tvBulan.setText(bulan);
        binding.tvTahun.setText(tahun);
        binding.btnBatal.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        // Membuat span dengan tampilan berbeda dan dapat diklik
        new PatternEditableBuilder().
                addPattern(Pattern.compile("Baca Detailnya"), GeneralHelper.getColor(R.color.colorTextBlueBri),
                        new PatternEditableBuilder.SpannableClickedListener() {
                            @Override
                            public void onSpanClicked(String text) {
                                mListener.onCLickPenalti();
                            }
                        }).into(binding.tvDeskripsi);

        binding.btnCairkan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mListener.onClickDeposito();
                dismiss();
            }
        });

        return binding.getRoot();
    }
}