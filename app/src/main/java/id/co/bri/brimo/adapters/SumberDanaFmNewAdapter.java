package id.co.bri.brimo.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemSumberDanaFmNewBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;

public class SumberDanaFmNewAdapter extends RecyclerView.Adapter<SumberDanaFmNewAdapter.ViewHolder> {

    private List<AccountModel> itemAccounts;
    private Context context;
    private long amount;

    ClickItem clickItem;

    public interface ClickItem {
        void onClickItem(AccountModel accountModel, int p);
    }


    public SumberDanaFmNewAdapter(List<AccountModel> itemAccounts, Context context, long j, ClickItem clickItem1) {
        this.itemAccounts = itemAccounts;
        this.context = context;
        this.amount = j;
        this.clickItem = clickItem1;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemSumberDanaFmNewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AccountModel account = itemAccounts.get(position);
        SaldoReponse saldoReponse = itemAccounts.get(position).getSaldoReponse();

        holder.binding.tvNoRek.setText(account.getAcoountString());

        if (account.getImagePath() != null) {
            if (!account.getImagePath().equalsIgnoreCase("")) {
                GeneralHelper.loadImageUrl(context, account.getImagePath(), holder.binding.ivIconRek, R.drawable.bri, 0);
            } else {
                holder.binding.ivIconRek.setImageResource(R.drawable.bri);
            }
        } else {
            holder.binding.ivIconRek.setImageResource(R.drawable.bri);
        }

        //condition untuk rekening utama
        if (account.getIsDefault() == 1)
            holder.binding.lyRekUtama.setVisibility(View.VISIBLE);
        else
            holder.binding.lyRekUtama.setVisibility(View.GONE);

        if (account.isSelected()) {
            holder.binding.lyMain.setBackgroundResource(GeneralHelper.getImageId(context, "bg_border_blue"));
        } else {
            holder.binding.lyMain.setBackgroundColor(GeneralHelper.getColor(R.color.white));
        }

        if (account.getAlias() != null && !account.getAlias().isEmpty()) {
            holder.binding.tvAliasRek.setText(account.getAlias());
        } else {
            holder.binding.tvAliasRek.setText(account.getName());
        }
        holder.itemView.setOnClickListener(view -> {
            account.setSelected(true);
            clickItem.onClickItem(account, position);

        });
    }

    public void updateAdapter(ClickItem clickItem, Context context, List<AccountModel> list) {
        this.itemAccounts = list;
        this.clickItem = clickItem;
        this.context = context;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return itemAccounts.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemSumberDanaFmNewBinding binding;

        public ViewHolder(@NonNull ItemSumberDanaFmNewBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

}
