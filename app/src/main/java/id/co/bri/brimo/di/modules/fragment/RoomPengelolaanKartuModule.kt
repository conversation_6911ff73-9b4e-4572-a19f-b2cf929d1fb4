package id.co.bri.brimo.di.modules.fragment

import android.content.Context
import androidx.room.Room
import dagger.Module
import dagger.Provides
import id.co.bri.brimo.data.dao.pengelolaankartu.PengelolaanKartuDao
import id.co.bri.brimo.data.db.PengelolaanKartuDatabase
import id.co.bri.brimo.data.repository.pengelolaankartu.PengelolaanKartuRepository
import id.co.bri.brimo.data.repository.pengelolaankartu.PengelolaanKartuSource
import id.co.bri.brimo.domain.config.DbConfig
import javax.inject.Singleton

@Module
class RoomPengelolaanKartuModule(context: Context) {

    private var pengelolaanKartuDb = Room.databaseBuilder(
        context = context.applicationContext,
        klass = PengelolaanKartuDatabase::class.java,
        name = DbConfig.DB_NAME_PENGELOLAAN_KARTU
    ).fallbackToDestructiveMigration().build()

    @Singleton
    @Provides
    fun providesPengelolaanKartuDatabase(): PengelolaanKartuDatabase {
        return pengelolaanKartuDb
    }

    @Provides
    @Singleton
    fun providePengelolaanKartuDao(appDatabase: PengelolaanKartuDatabase): PengelolaanKartuDao {
        return appDatabase.listPengelolaanKartuDao()
    }

    @Singleton
    @Provides
    fun providePengelolaanKartuSource(pengelolaanKartuDao: PengelolaanKartuDao): PengelolaanKartuSource {
        return PengelolaanKartuRepository(pengelolaanKartuDao)
    }
}