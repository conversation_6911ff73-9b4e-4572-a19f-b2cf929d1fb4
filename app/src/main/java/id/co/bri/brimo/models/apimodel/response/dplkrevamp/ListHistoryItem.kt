package id.co.bri.brimo.models.apimodel.response.dplkrevamp

import com.google.gson.annotations.SerializedName

data class ListHistoryItem(
    @field:SerializedName("detail")
    val detail: ArrayList<DetailItem> = ArrayList(),

    @field:SerializedName("title")
    val title: String = "",

    @field:SerializedName("value")
    val value: String = ""
)
data class DetailItem(
    @field:SerializedName("date")
    val date: String = "",

    @field:SerializedName("total")
    val total: Total = Total(),

    @field:SerializedName("transaction_value")
    val transactionValue: TransactionValue = TransactionValue()
)
data class TransactionValue(
    @field:SerializedName("title")
    val title: String = "",

    @field:SerializedName("description")
    val description: String = "",

    @field:SerializedName("timestamp")
    val timestamp: String = "",

    @field:SerializedName("status")
    val status: String = ""
)
