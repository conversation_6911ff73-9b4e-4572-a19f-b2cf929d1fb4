package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class InfoDplkResponse {

    @SerializedName("dplk")
    @Expose
    private List<Dplk> dplkList;

    public List<Dplk> getDplkList() {
        return dplkList;
    }

    public void setDplkList(List<Dplk> dplkList) {
        this.dplkList = dplkList;
    }

    public static class Dplk {
        @SerializedName("account_number")
        @Expose
        private String accountNumber;
        @SerializedName("account_name")
        @Expose
        private String accountName;
        @SerializedName("saving_account")
        @Expose
        private String savingAccount;
        @SerializedName("total_balance")
        @Expose
        private Double totalBalance;
        @SerializedName("total_balance_string")
        @Expose
        private String totalBalanceString;
        @SerializedName("account_list")
        @Expose
        private List<AccountDplkResponse> accountLists;
        @SerializedName("is_pop_up")
        @Expose
        private Boolean isPopUp;

        public Dplk(String accountNumber, String accountName, String savingAccount, Double totalBalance, String totalBalanceString, List<AccountDplkResponse> accountLists, Boolean isPopUp) {
            this.accountNumber = accountNumber;
            this.accountName = accountName;
            this.savingAccount = savingAccount;
            this.totalBalance = totalBalance;
            this.totalBalanceString = totalBalanceString;
            this.accountLists = accountLists;
            this.isPopUp = isPopUp;
        }

        public String getAccountNumber() {
            return accountNumber;
        }

        public void setAccountNumber(String accountNumber) {
            this.accountNumber = accountNumber;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getSavingAccount() {
            return savingAccount;
        }

        public void setSavingAccount(String savingAccount) {
            this.savingAccount = savingAccount;
        }

        public List<AccountDplkResponse> getAccountLists() {
            return accountLists;
        }

        public void setAccountLists(List<AccountDplkResponse> accountLists) {
            this.accountLists = accountLists;
        }

        public Double getTotalBalance() {
            return totalBalance;
        }

        public void setTotalBalance(Double totalBalance) {
            this.totalBalance = totalBalance;
        }

        public String getTotalBalanceString() {
            return totalBalanceString;
        }

        public void setTotalBalanceString(String totalBalanceString) {
            this.totalBalanceString = totalBalanceString;
        }

        public Boolean getPopUp() {
            return isPopUp;
        }

        public void setPopUp(Boolean popUp) {
            isPopUp = popUp;
        }
    }
}