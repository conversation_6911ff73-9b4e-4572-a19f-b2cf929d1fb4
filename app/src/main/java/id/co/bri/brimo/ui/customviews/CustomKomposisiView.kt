package id.co.bri.brimo.ui.customviews

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import id.co.bri.brimo.R
import kotlin.math.log

class CustomKomposisiView(context: Context, attrs: AttributeSet?) : View(context, attrs) {

    private var paint: Paint
    private var percentages = floatArrayOf() // Persentase masing-masing warna
    private var assetColors = LinkedHashMap<String, Int>()

    init {
        // Inisialisasi paint untuk mengatur warna dan gaya gambar
        paint = Paint()
        paint.style = Paint.Style.FILL_AND_STROKE
        paint.strokeWidth = 5f // Ganti dengan ketebalan garis yang diinginkan
    }

    fun setPercentages(percentages: FloatArray,assetNames: List<String>) {
        this.percentages = percentages
        calculateAssetColors(assetNames)
        invalidate() // Meminta tampilan untuk digambar ulang
    }

    private fun calculateAssetColors(assetNames: List<String>) {
        // Set default color for assets
        assetColors.clear()
        for (assetName in assetNames) {
            assetColors[assetName] = when (assetName) {
                "Deposito" -> R.color.neutral_dark40
                "Emas" -> R.color.primary_blue80
                "SBN" -> R.color.primary_blue60
                "DPLK" -> R.color.primary_purple60
                "Rencana" -> R.color.primary_orange80
                else -> R.color.neutral_dark40
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val width = width.toFloat()
        val height = height.toFloat()
        val totalPercentage = percentages.sum()

        if (percentages.isNotEmpty()) {
            var left = 0f

            for (i in 0 until percentages.size) {
                val percentage = percentages[i] / totalPercentage

                // Skip drawing if the percentage is zero
                if (percentage > 0) {
                    val right = left + (width * percentage)

                    // Get the color for the current asset name dynamically
                    val assetName = assetColors.keys.elementAt(i)
                    paint.color = ContextCompat.getColor(context, assetColors[assetName] ?: R.color.black)

                    // Draw the colored rectangles without rounded corners
                    val rectF = RectF(left, 0f, right, height)
                    canvas.drawRect(rectF, paint)

                    left = right
                }
            }
        }
    }
}
