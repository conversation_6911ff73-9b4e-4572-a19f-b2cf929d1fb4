package id.co.bri.brimo.ui.activities.deposito;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.JangkaWaktuDepositoAdapter;
import id.co.bri.brimo.contract.IPresenter.deposito.ITambahDaftarDepositoPresenter;
import id.co.bri.brimo.contract.IView.deposito.ITambahDaftarDepositoView;
import id.co.bri.brimo.databinding.ActivityTambahDaftarDepositoBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.openDepoInquiryRequest;
import id.co.bri.brimo.models.apimodel.response.DepositoOpenOption;
import id.co.bri.brimo.models.apimodel.response.DepositoOpenResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.JenisPerpanjanganDepositoFragment;

public class TambahDaftarDepositoActivity extends BaseActivity implements
        ITambahDaftarDepositoView,
        JenisPerpanjanganDepositoFragment.OnClickPerpanjangan,
        AmountFormatWatcher.onAmountChange,
        JangkaWaktuDepositoAdapter.OnAddButtonListener,
        View.OnClickListener {

    private ActivityTambahDaftarDepositoBinding binding;

    protected static final String TAG_RESPONSE = "response";

    protected long nominalAwal = 0;
    protected boolean isCheck = false;

    protected String productId;
    protected String renewalType;

    @Inject
    ITambahDaftarDepositoPresenter<ITambahDaftarDepositoView> presenter;

    protected static DepositoOpenResponse depositoOpenResponse;
    protected List<DepositoOpenOption> optionList = new ArrayList<>();
    protected JangkaWaktuDepositoAdapter adapter;
    protected List<Double> doubleList = new ArrayList<>();

    public static void launchIntent(Activity caller, DepositoOpenResponse response) {
        Intent intent = new Intent(caller, TambahDaftarDepositoActivity.class);
        depositoOpenResponse = response;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTambahDaftarDepositoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, getTitleBar());

        injectDependency();
        setupViews();

        binding.etJenisPerpanjangan.setOnClickListener(this);
        binding.btnLanjut.setOnClickListener(this);
    }

    protected String getTitleBar() {
        return GeneralHelper.getString(R.string.toolbar_deposito);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_deposito_open_inquiry));
            presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_deposito_open_confirmation));
            presenter.setUrlPayment(GeneralHelper.getString(R.string.url_deposito_open_payment));
        }
    }

    private void setupViews() {
        if (depositoOpenResponse != null){
            binding.tvRentangNominal.setText("Dalam rentang " + depositoOpenResponse.getMinimumAmountString() + " - " + depositoOpenResponse.getMaximumAmountString());

            binding.recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
            binding.recyclerView.smoothScrollToPosition(0);
            binding.recyclerView.setHasFixedSize(true);

            adapter = new JangkaWaktuDepositoAdapter(this, depositoOpenResponse.getProductList(), this::callback);
            binding.recyclerView.setAdapter(adapter);
            adapter.notifyDataSetChanged();

            binding.etPenempatanAwal.addTextChangedListener(new AmountFormatWatcher(binding.etPenempatanAwal, this, false));
            binding.etPenempatanAwal.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    //  Do nothing
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    if (binding.etPenempatanAwal.getText().length() != 0)
                        nominalAwal = Long.parseLong(binding.etPenempatanAwal.getText().toString().replace(".", "")
                                .replace("Rp", "").replace("-", ""));
                    else
                        nominalAwal = 0;
                    setupAmount();
                    validasiText();
                }

                @Override
                public void afterTextChanged(Editable s) {
                    // Do nothing
                }
            });

            binding.etJenisPerpanjangan.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    //  Do nothing
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    validasiText();
                }

                @Override
                public void afterTextChanged(Editable s) {
                    // Do nothing
                }
            });
        }

    }

    protected void validasiText() {
        if (binding.etPenempatanAwal.getText().length() != 0 && isCheck && binding.etJenisPerpanjangan.getText().toString().length() != 0) {
            validasiButton(nominalAwal >= depositoOpenResponse.getMinimumAmount() &&
                    nominalAwal <= depositoOpenResponse.getMaximumAmount());
        } else validasiButton(false);
    }

    protected void validasiButton(boolean isEnable) {
        if (isEnable) {
            binding.btnLanjut.setEnabled(true);
            binding.btnLanjut.setAlpha(1);
        } else {
            binding.btnLanjut.setEnabled(false);
            binding.btnLanjut.setAlpha(0.3f);
        }
    }

    @Override
    public void setItemClick(DepositoOpenOption option) {
        renewalType = option.getCode();
        binding.etJenisPerpanjangan.setText(option.getName());
    }

    @Override
    public void onAmountChange(String amount) {
        // do nothing
    }

    private void setupAmount() {
        double hasil;
        double finalHasil;

        doubleList.clear();
        for (int i = 0; i < depositoOpenResponse.getProductList().size(); i++) {
            hasil = (nominalAwal * depositoOpenResponse.getProductList().get(i).getRate() / 100
                    * depositoOpenResponse.getProductList().get(i).getDays() / 365
                    * 0.8);
            finalHasil = nominalAwal + hasil;
            doubleList.add(Double.valueOf(Math.round(finalHasil)));
        }

        adapter = new JangkaWaktuDepositoAdapter(this, depositoOpenResponse.getProductList(), doubleList, this::callback);
        binding.recyclerView.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void setAmountListener() {
        // Do nothing
    }

    @Override
    public void callback(DepositoOpenResponse.Product depositoRes, int position) {
        productId = depositoRes.getId();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            depositoOpenResponse.getProductList().forEach(tO -> tO.setCheck(false));
        } else {
            for (DepositoOpenResponse.Product p : depositoOpenResponse.getProductList()) {
                p.setCheck(false);
            }
        }

        depositoOpenResponse.getProductList().get(position).setCheck(true);
        if (optionList != null) {
            optionList.clear();
            optionList.addAll(depositoOpenResponse.getProductList().get(position).getOption());

            binding.etJenisPerpanjangan.setText("");
            binding.layoutPerpanjangan.setVisibility(View.VISIBLE);
            binding.layoutPerpanjangan.setAnimation(animationDown);
        }
        isCheck = true;
        validasiText();

        adapter.notifyDataSetChanged();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_jenis_perpanjangan:
                if (!optionList.isEmpty()) {
                    JenisPerpanjanganDepositoFragment bottomFragment = new JenisPerpanjanganDepositoFragment(this, optionList);
                    bottomFragment.setCancelable(true);
                    bottomFragment.show(getSupportFragmentManager(), "");
                }
                break;
            case R.id.btn_lanjut:
                openDepoInquiryRequest request = new openDepoInquiryRequest(productId, String.valueOf(nominalAwal), renewalType);
                presenter.sendDataInquiry(request);
                break;
            default:
                break;
        }
    }

    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();
        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal");
        parameterModel.setStringButtonSubmit("Lanjut");
        return parameterModel;
    }

    @Override
    public void getDataSuccess(InquiryOpenRencanaResponse inquiryResponse, String urlKonfirmasi, String urlPayment, boolean fromFastmenu) {
        InquiryOpenDepositoActivity.launchIntent(this, inquiryResponse, urlKonfirmasi, urlPayment, getTitleBar(), setParameter(), fromFastmenu);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data);
                if (data != null && !data.getStringExtra(Constant.TAG_ERROR_MESSAGE).isEmpty())
                    this.finish();

            } else {
                this.setResult(RESULT_CANCELED);
                finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}