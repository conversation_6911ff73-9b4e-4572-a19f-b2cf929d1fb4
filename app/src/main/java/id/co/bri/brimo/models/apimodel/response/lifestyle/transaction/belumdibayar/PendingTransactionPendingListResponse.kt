package id.co.bri.brimo.models.apimodel.response.lifestyle.transaction.belumdibayar


import com.google.gson.annotations.SerializedName

data class PendingTransactionPendingListResponse(
    @SerializedName("feature_code")
    val featureCode: String = "", // T1KA
    @SerializedName("icon_name")
    val iconName: String = "", // kai.png
    @SerializedName("icon_path")
    val iconPath: String = "", // http://*************:4010/brimo-asset/icon/lifestyle/dashboard/menu/travel/kai.png
    @SerializedName("id")
    val id: String = "", // 212683491045157
    @SerializedName("menu_type")
    val menuType: String = "", // 1
    @SerializedName("subtitle")
    val subtitle: String = "", // Dibeli pada 10 Jan 2023 08:53
    @SerializedName("title")
    val title: String = "", // Tiket <PERSON>
    @SerializedName("inquiry_data")
    val tripData: PendingPurchaseTripDataResponse = PendingPurchaseTripDataResponse()
)