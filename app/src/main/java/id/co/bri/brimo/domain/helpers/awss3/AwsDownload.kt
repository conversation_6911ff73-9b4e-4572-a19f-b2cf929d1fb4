package id.co.bri.brimo.domain.helpers.awss3

import android.content.Context
import com.amazonaws.ClientConfiguration
import com.amazonaws.Protocol
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.regions.Region
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.S3ClientOptions
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest
import id.co.bri.brimo.BuildConfig
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.security.MyCryptStatic
import java.net.URL
import java.util.*

class AwsDownload(
    private val context: Context,
    private val bucket: String
) {

    @Suppress("DEPRECATION")
    fun downloadFile(fileName: String): URL {
        val configuration = ClientConfiguration()
        configuration.maxErrorRetry = 3
        configuration.connectionTimeout = 501000
        configuration.socketTimeout = 501000
        configuration.protocol = Protocol.HTTP
        val options = S3ClientOptions()
        options.isPathStyleAccess = true
        val credentials = BasicAWSCredentials(
            AppConfig.getAccessKeyMinio(), AppConfig.getSecretKeyMinio()
        )

        val s3 = AmazonS3Client(credentials, Region.getRegion(AppConfig.REGION))
        s3.endpoint = AppConfig.getURLMinio()
        s3.setS3ClientOptions(options)

        val expires = Date(Date().time + 2000 * 60)
        val generatePresignedUrlRequest =
            GeneratePresignedUrlRequest(bucket, fileName)
        generatePresignedUrlRequest.expiration = expires
        return s3.generatePresignedUrl(generatePresignedUrlRequest)
    }
}