package id.co.bri.brimo.adapters.transactionlimitinformation

import android.opengl.Visibility
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemInformationLimitPremiumBinding
import id.co.bri.brimo.databinding.ItemInformationLimitRegularBinding
import id.co.bri.brimo.models.apimodel.response.transactionlimitinformation.TransactionLimitInformationData

class TransactionLimitInformationAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_PREMIUM = 0
        private const val VIEW_TYPE_REGULAR = 1
    }

    var transactionList = mutableListOf<TransactionLimitInformationData>()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var isSafetyMode = false
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var isUsedLimit = false
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun getItemViewType(position: Int): Int {
        val transaction = transactionList[position]
        return if (transaction.priorityId == 1) {
            VIEW_TYPE_PREMIUM
        } else {
            VIEW_TYPE_REGULAR
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_PREMIUM -> {
                val binding = ItemInformationLimitPremiumBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                PremiumTypeHolder(binding)
            }

            VIEW_TYPE_REGULAR -> {
                val binding = ItemInformationLimitRegularBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                RegularTypeHolder(binding)
            }

            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemCount(): Int = transactionList.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val transaction = transactionList[position]
        when (holder) {
            is RegularTypeHolder -> holder.bindData(transaction)
            is PremiumTypeHolder -> holder.bindData(transaction)
        }
    }

    inner class RegularTypeHolder(val binding: ItemInformationLimitRegularBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(transaction: TransactionLimitInformationData) {
            Glide.with(binding.root.context)
                .load(transaction.iconPath)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .placeholder(R.drawable.ic_pembayaran_blue)
                .into(binding.ivIcon)
            binding.tvTitle.text = transaction.title
            binding.tvDesc.text = transaction.subtitle
            binding.run {
                if (isUsedLimit) {
                    llWithoutUsedLimit.visibility = View.GONE
                    llWithUsedLimit.visibility = View.VISIBLE
                    tvUsedLimit.text = transaction.usedLimitString
                    tvMaxLimit.text = transaction.limitString
                } else {
                    llWithUsedLimit.visibility = View.GONE
                    llWithoutUsedLimit.visibility = View.VISIBLE
                    tvMasterLimit.text = transaction.limitString
                }
            }
            val imageResource =
                if (isSafetyMode) R.drawable.ic_guard_regular else R.drawable.bg_limit_card_regular
            binding.ivGuard.setImageResource(imageResource)
        }
    }

    inner class PremiumTypeHolder(val binding: ItemInformationLimitPremiumBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(transaction: TransactionLimitInformationData) {
            Glide.with(binding.root.context)
                .load(transaction.iconPath)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .placeholder(R.drawable.ic_pembayaran_black)
                .into(binding.ivIcon)
            binding.tvTitle.text = transaction.title
            binding.tvDesc.text = transaction.subtitle
            binding.run {
                if (isUsedLimit) {
                    llWithoutUsedLimit.visibility = View.GONE
                    llWithUsedLimit.visibility = View.VISIBLE
                    tvUsedLimit.text = transaction.usedLimitString
                    tvMaxLimit.text = transaction.limitString
                } else {
                    llWithUsedLimit.visibility = View.GONE
                    llWithoutUsedLimit.visibility = View.VISIBLE
                    tvMasterLimit.text = transaction.limitString
                }
            }
            val imageResource =
                if (isSafetyMode) R.drawable.ic_guard_premium else R.drawable.bg_limit_card_premium
            binding.ivGuard.setImageResource(imageResource)
        }
    }
}