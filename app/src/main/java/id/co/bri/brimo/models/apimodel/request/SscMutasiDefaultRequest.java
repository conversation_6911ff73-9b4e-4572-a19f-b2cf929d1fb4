package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class SscMutasiDefaultRequest {
    @SerializedName("feature_id")
    @Expose
    private String featureId;

    public SscMutasiDefaultRequest(String featureId) {
        this.featureId = featureId;
    }

    public String getFeatureId() {
        return featureId;
    }

    public void setFeatureId(String featureId) {
        this.featureId = featureId;
    }
}
