package id.co.bri.brimo.adapters.loaninapp

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemHistoryTypeLoanInAppBinding
import id.co.bri.brimo.domain.config.FontConfig
import id.co.bri.brimo.models.loaninapp.LoanInAppHistoryTypeModel

class HistoryTypeAdapter(
    private val mContext: Context,
    private val onClickItemListener: OnClickItemListener,
    private val listType: MutableList<LoanInAppHistoryTypeModel>
): RecyclerView.Adapter<HistoryTypeAdapter.MyViewHolder>() {

    private var selectedPosition = 0

    interface OnClickItemListener {
        fun onClickItem(position: Int, type: String?)
    }

    inner class MyViewHolder(val binding: ItemHistoryTypeLoanInAppBinding) :
            RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HistoryTypeAdapter.MyViewHolder =  MyViewHolder(
        ItemHistoryTypeLoanInAppBinding.inflate(
            LayoutInflater.from(mContext),
            parent,
            false
        )
    )

    override fun onBindViewHolder(
        holder: HistoryTypeAdapter.MyViewHolder,
        position: Int
    ) = with(holder.binding) {
        val data = listType[position]
        var customFont: Typeface

        tvType.text = data.type
        llHistoryType.setOnClickListener {
            onClickItemListener.onClickItem(holder.adapterPosition, listType[holder.adapterPosition].type)
        }

        if (selectedPosition == position) {
            tvType.setTextColor(mContext.resources.getColor(R.color.primary_blue100))
            customFont = Typeface.createFromAsset(mContext.assets, FontConfig.BRI_DIGITAL_TEXT_BOLD)
            tvType.typeface = customFont
            llHistoryType.backgroundTintList = ContextCompat.getColorStateList(mContext, R.color.neutral_baseWhite)
        } else {
            tvType.setTextColor(mContext.resources.getColor(R.color.neutral_baseWhite))
            customFont = Typeface.createFromAsset(mContext.assets, FontConfig.BRI_DIGITAL_TEXT_MEDIUM)
            tvType.typeface = customFont
            llHistoryType.backgroundTintList = ContextCompat.getColorStateList(mContext, R.color.primary_blue100)
        }
    }

    override fun getItemCount(): Int = listType.size

    fun setSelectedPosition(position: Int) {
        selectedPosition = position
        notifyDataSetChanged()
    }
}