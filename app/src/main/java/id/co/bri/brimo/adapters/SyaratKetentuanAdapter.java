package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;


import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemSyaratKetentuanBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.TermConditionModel;

public class SyaratKetentuanAdapter extends RecyclerView.Adapter<SyaratKetentuanAdapter.ViewHolder> {

    private List<TermConditionModel> mTotalDataViews;
    private Context context;
    private ActionAdapter mActionAdapter;
    private boolean mIsTextView = false;
    private boolean mIsSkipAble = false;

    public SyaratKetentuanAdapter(List<TermConditionModel> totalDataViews, Context context, ActionAdapter actionAdapter) {
        this.mTotalDataViews = totalDataViews;
        this.context = context;
        this.mActionAdapter = actionAdapter;
    }

    public SyaratKetentuanAdapter(List<TermConditionModel> totalDataViews, Context context, ActionAdapter actionAdapter, Boolean isTextView, Boolean isSkipAble) {
        this.mTotalDataViews = totalDataViews;
        this.context = context;
        this.mActionAdapter = actionAdapter;
        this.mIsTextView = isTextView;
        this.mIsSkipAble = isSkipAble;
    }

    public interface ActionAdapter {
        void launchTnc(TermConditionModel termConditionModel, int tncPosition);

        void setTncStatus(boolean isTrue, int tncPosition);
    }


    @NonNull
    @Override
    public SyaratKetentuanAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemSyaratKetentuanBinding.inflate(LayoutInflater.from(context), parent, false));
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onBindViewHolder(@NonNull SyaratKetentuanAdapter.ViewHolder holder, int position) {
        TermConditionModel termConditionModel = mTotalDataViews.get(position);
        if (mIsTextView) {
            int i1 = termConditionModel.getTitle().indexOf("[");
            int i2 = termConditionModel.getTitle().lastIndexOf("]");

            holder.binding.tvTnc.setText(GeneralHelper.spannableText(termConditionModel.getTitle().replaceAll("[\\[\\]]", ""), i1, i2 - 1, ContextCompat.getColor(context, R.color.primaryColor)));
            holder.binding.wvTnc.setVisibility(View.GONE);
            holder.binding.tvTnc.setVisibility(View.VISIBLE);
        } else {
            holder.binding.tvTnc.setVisibility(View.GONE);
            holder.binding.wvTnc.setVisibility(View.VISIBLE);
            GeneralHelper.setWebViewStandart(holder.binding.wvTnc, "", termConditionModel.getTitle());
        }

        WebSettings webSetting = holder.binding.wvTnc.getSettings();
        webSetting.setDefaultFontSize((12));

        holder.binding.wvTnc.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_MOVE) {
                return false;
            }

            if (event.getAction() == MotionEvent.ACTION_UP) {
                mActionAdapter.launchTnc(termConditionModel, position);
                return false;
            }

            return false;
        });
        holder.binding.checkbox2.setChecked(termConditionModel.isChecked());

        holder.binding.tvTnc.setOnClickListener(v -> {
            mActionAdapter.launchTnc(termConditionModel, position);
            mActionAdapter.setTncStatus(false, position);
        });

        holder.binding.checkbox2.setOnClickListener(view1 -> {
            if (holder.binding.checkbox2.isChecked()) {
                if (!mIsSkipAble) {
                    mActionAdapter.launchTnc(termConditionModel, position);
                }
                mActionAdapter.setTncStatus(true, position);
            } else {
                mActionAdapter.setTncStatus(false, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mTotalDataViews.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemSyaratKetentuanBinding binding;

        public ViewHolder(@NonNull ItemSyaratKetentuanBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
