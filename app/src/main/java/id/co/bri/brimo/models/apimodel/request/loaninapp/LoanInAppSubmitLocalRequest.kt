package id.co.bri.brimo.models.apimodel.request.loaninapp

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class LoanInAppSubmitLocalRequest(
    var username: String = "",

    var cardCcNo: String = "",
    var cardCcName: String = "",
    var cardCcImageSlim: String = "",
    var cardCcImageCrop: String = "",
    var cardCcToken: String = "",
    var cardCcLimit: String = "",
    var balance: Double = 0.0,
    var balanceString: String = "",

    var cardAccountNo: String = "",
    var cardAccountNoString: String = "",
    var cardAccountName: String = "",
    var cardAccountAlias: String = "",
    var cardAccountImage: String = "",

    var amount: Long = 0L,
    var amountString: String = "",
    var term: Int = 0,
    var termString: String = "",
    var termSimulationCode: String = "",
    var interestRate: Double = 0.0,
    var interestRateString: String = "",
    var adminFee: Long = 0,
    var adminFeeString: String = "",
    var monthlyPayment: Long = 0,
    var monthlyPaymentString: String = "",

    var tnc: String = "",
    var otp: String = "",
    var serverId: String = "",
    var otpType: String = "",

    var limitCc: Int = 0
) : Parcelable

fun LoanInAppSubmitLocalRequest.toLoanInAppSubmitRequest() = LoanInAppSubmitRequest(
    accountName = cardAccountName,
    accountNo = cardAccountNo,
    adminFee = adminFee,
    amount = amount,
    cardToken = cardCcToken,
    interestRate = interestRate,
    monthlyPayment = monthlyPayment,
    otp = otp,
    serverId = serverId,
    simulationCode = termSimulationCode,
    term = term,
    username = username,
    otpType = otpType
)