package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListRiwayatPLNAdapter;
import id.co.bri.brimo.contract.IPresenter.cetakToken.ICetakTokenPresenter;
import id.co.bri.brimo.contract.IView.cetakToken.ICetakTokenView;
import id.co.bri.brimo.databinding.ActivityCetakTokenBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.InboxResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity;

public class CetakTokenActivity extends BaseActivity implements View.OnClickListener,
        ICetakTokenView,
        SwipeRefreshLayout.OnRefreshListener,
        ListRiwayatPLNAdapter.onClickItem,
        ListRiwayatPLNAdapter.onSelected {

    private ActivityCetakTokenBinding binding;

    protected SkeletonScreen skeletonScreenInbox;
    protected ListRiwayatPLNAdapter listRiwayatPLNAdapter;
    protected LinearLayoutManager layoutManager;
    protected List<InboxResponse.ActivityList> activityLists = new ArrayList<>();

    protected String periode = "";
    protected String status = "";
    protected String fitur = "";
    protected String subFitur = "";
    protected String lastId = "";

    protected Handler handler = new Handler();
    private boolean isLoading = false;


    @Inject
    ICetakTokenPresenter<ICetakTokenView> cetakTokenPresenter;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, CetakTokenActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCetakTokenBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        if (binding.tbCetakToken != null) {
            GeneralHelper.setToolbar(this, binding.tbCetakToken.toolbar, GeneralHelper.getString(R.string.history_toolbar));
        }

        initiateAdapter();

        binding.swipeRefresh.setOnRefreshListener(this);
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (cetakTokenPresenter != null) {
            cetakTokenPresenter.setView(this);
            cetakTokenPresenter.start();
            callService();

        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (cetakTokenPresenter != null) {
            callService();
        }


    }


    /**
     * panggil service
     */
    public void callService() {
        if (cetakTokenPresenter != null) {
            lastId = "0";

            if (skeletonScreenInbox != null) {
                skeletonScreenInbox.show();
            }

            cetakTokenPresenter.setUrlCetak(GeneralHelper.getString(R.string.cetak_token_pln));
            cetakTokenPresenter.setUrlDetailCetak(GeneralHelper.getString(R.string.url_activity_detail));
            cetakTokenPresenter.getCetakToken(lastId, true);
        }
    }

    /**
     * inisialisasi adapter
     */
    public void initiateAdapter() {
        layoutManager = new LinearLayoutManager(this, RecyclerView.VERTICAL, false);
        binding.rvItemInbox.setLayoutManager(layoutManager);
        listRiwayatPLNAdapter = new ListRiwayatPLNAdapter(activityLists, this, this, this);
        binding.rvItemInbox.setAdapter(listRiwayatPLNAdapter);

        binding.rvItemInbox.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();

                if (!isLoading) {
                    if (linearLayoutManager != null && linearLayoutManager.findLastCompletelyVisibleItemPosition() == activityLists.size() - 1) {
                        if (linearLayoutManager.getItemCount() >= 10) {
                            loadMoreInbox();
                            isLoading = true;
                        }
                    }
                }
            }
        });

        skeletonScreenInbox = Skeleton.bind(binding.rvItemInbox)
                .adapter(listRiwayatPLNAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(7)
                .load(R.layout.item_skeleton_inbox)
                .show();
    }


    /**
     * Token Berhasil
     *
     * @param inboxResponse
     * @param isRefresh
     */
    @Override
    public void onSuccessGetCetakToken(InboxResponse inboxResponse, boolean isRefresh) {
        if (inboxResponse != null) {
            if (isRefresh) {
                if (activityLists != null) {
                    activityLists.clear();
                }
            }
            if (binding.swipeRefresh != null) {
                binding.swipeRefresh.setRefreshing(false);
            }
            if (skeletonScreenInbox != null) {
                skeletonScreenInbox.hide();
            }

            try {
                if (activityLists != null) {
                    binding.llDesc12.setVisibility(View.GONE);
                    binding.noDataSaved.setVisibility(View.GONE);
                    binding.rvItemInbox.setVisibility(View.VISIBLE);

                    binding.rvItemInbox.smoothScrollToPosition(activityLists.size());
                    activityLists.addAll(inboxResponse.getActivityLists());
                    listRiwayatPLNAdapter.setItems(activityLists);
                    listRiwayatPLNAdapter.notifyDataSetChanged();
                }
            } catch (Exception e) {
                // do nothing
            }

            isLoading = false;
        }
    }

    @Override
    public void onSuccessGetDetailToken(ReceiptRevampInboxResponse receiptResponse) {
        if (receiptResponse.getReceiptRevampResponse().isOnProcess()) {
            ReceiptAbnormalRevampActivity.launchIntent(this, receiptResponse.getReceiptRevampResponse(), isFromFastMenu, defaultIcon());
        } else {
            ReceiptRevampActivity.launchIntent(this, receiptResponse.getReceiptRevampResponse(), defaultIcon());
        }
    }

    private int defaultIcon() {
        return R.drawable.ic_pln_polos;
    }

    @Override
    public void onSuccessGetDetailTokenNonRevamp(ReceiptResponse receiptResponse) {
        // do nothing
    }

    @Override
    public void onCetakEnd(String message) {
        if (activityLists.size() == 0) {
            if (binding.swipeRefresh != null) {
                binding.swipeRefresh.setRefreshing(false);
            }
            binding.noDataSaved.setVisibility(View.VISIBLE);
            binding.rvItemInbox.setVisibility(View.GONE);
            binding.llDesc12.setVisibility(View.GONE);
        }
    }

    @Override
    public void onException12() {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        binding.rvItemInbox.setVisibility(View.GONE);
        binding.llDesc12.setVisibility(View.VISIBLE);
    }

    @Override
    public void onException93() {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }

        binding.rvItemInbox.setVisibility(View.GONE);
        binding.llDesc12.setVisibility(View.VISIBLE);
    }

    @Override
    public void onException(String message) {
        if (binding.swipeRefresh != null) {
            binding.swipeRefresh.setRefreshing(false);
        }
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(this, message);
        else
            GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.contentInbox), message);
    }

    private void loadMoreInbox() {
        activityLists.add(null);
        listRiwayatPLNAdapter.notifyItemInserted(activityLists.size() - 1);

        handler.postDelayed(() -> {
            activityLists.remove(activityLists.size() - 1);
            int scrollPosition = activityLists.size();
            listRiwayatPLNAdapter.notifyItemRemoved(scrollPosition);
            cetakTokenPresenter.getCetakToken(lastId, false);
        }, 1000);
    }

    @Override
    public void onClick(View view) {

    }

    /**
     * Merefresh Data inbox
     */
    @Override
    public void onRefresh() {
        if (skeletonScreenInbox != null) {
            binding.rvItemInbox.setVisibility(View.VISIBLE);
            skeletonScreenInbox.show();
            binding.llDesc12.setVisibility(View.GONE);
            binding.noDataSaved.setVisibility(View.GONE);
        }
        cetakTokenPresenter.getCetakToken("0", true);
    }

    /**
     * @param position
     */
    @Override
    public void onClickDetail(int position) {
        cetakTokenPresenter.getCetakDetail(activityLists.get(position).getReferenceNumber());
    }

    /**
     * @param position mengambil posisi id per inbox
     */
    @Override
    public void onSelectedItemId(int position) {
        if (position == activityLists.size() - 1) {
            lastId = activityLists.get(position).getId();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_NON_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        cetakTokenPresenter.stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        cetakTokenPresenter.stop();
        super.onBackPressed();
    }
}