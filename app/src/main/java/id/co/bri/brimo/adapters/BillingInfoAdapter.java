package id.co.bri.brimo.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ItemBillingInfoBinding;
import id.co.bri.brimo.models.BillingDetail;

import java.util.List;

import io.rmiri.skeleton.master.AdapterSkeleton;

public class BillingInfoAdapter extends AdapterSkeleton<BillingDetail, BillingInfoAdapter.MyViewHolder> {

    private List<BillingDetail> billingInfos;

    public BillingInfoAdapter(Context context, List<BillingDetail> billingInfo) {
        this.billingInfos = billingInfo;
    }

    @NonNull
    @Override
    public BillingInfoAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ItemBillingInfoBinding.inflate(LayoutInflater.from(parent.getContext()),
                parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull BillingInfoAdapter.MyViewHolder holder, int position) {
        BillingDetail billingInfo = billingInfos.get(position);
        holder.binding.tvName.setText(billingInfo.getName());
        holder.binding.tvValue.setText(billingInfo.getValue());
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        ItemBillingInfoBinding binding;

        public MyViewHolder(@NonNull ItemBillingInfoBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public int getItemCount() {
        return billingInfos.size();
    }

}
