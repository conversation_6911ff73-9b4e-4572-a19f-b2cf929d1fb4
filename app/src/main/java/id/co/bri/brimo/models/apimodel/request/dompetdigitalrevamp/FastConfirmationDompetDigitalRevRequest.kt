package id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.request.FastMenuRequest

class FastConfirmationDompetDigitalRevRequest(
    fastMenuRequest: FastMenuRequest,
    @field:Expose
    @field:SerializedName("reference_number") var referenceNumber: String,
    @field:Expose
    @field:SerializedName("account_number") var accountNumber: String,
    @field:Expose
    @field:SerializedName("amount") var amount: String,
    @field:Expose
    @field:SerializedName("save_as") var saveAs: String,
    @field:Expose
    @field:SerializedName("note") var note: String
) : FastMenuRequest(fastMenuRequest.username, fastMenuRequest.tokenKey)