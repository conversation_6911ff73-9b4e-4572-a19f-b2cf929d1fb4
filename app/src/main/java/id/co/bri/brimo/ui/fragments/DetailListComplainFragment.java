package id.co.bri.brimo.ui.fragments;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListComplainAdapter;
import id.co.bri.brimo.databinding.FragmentDetailListComplainBinding;
import id.co.bri.brimo.models.apimodel.response.ssc.ListComplainResponse;

public class DetailListComplainFragment extends Fragment implements ListComplainAdapter.OnClickItem {

    private FragmentDetailListComplainBinding binding;

    protected static final String TAG_RESPONSE = "response";
    protected static final String TAG_ISFIRST = "first";

    private List<ListComplainResponse.Status> statusList = new ArrayList<>();
    private ListComplainAdapter listComplainAdapter;
    private SkeletonScreen skeletonScreen;
    private OnDetailClick onDetailClick;
    private boolean isFirst;

    public interface OnDetailClick {
        void callback(ListComplainResponse.Status status, int position);
    }

    public static DetailListComplainFragment newInstance(String data, boolean isFirst) {
        DetailListComplainFragment fragment = new DetailListComplainFragment();
        Bundle args = new Bundle();
        args.putString(TAG_RESPONSE, data);
        args.putBoolean(TAG_ISFIRST, isFirst);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof OnDetailClick) {
            onDetailClick = (OnDetailClick) context;
        } else {
            throw new RuntimeException(context.toString() + " must implement OnDetailClick");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            if (!getArguments().getString(TAG_RESPONSE).isEmpty()) {
                Type collectionType = new TypeToken<Collection<ListComplainResponse.Status>>() {
                }.getType();
                Collection<ListComplainResponse.Status> enums = new Gson().fromJson(getArguments().getString(TAG_RESPONSE), collectionType);
                if (enums != null)
                    statusList.addAll(enums);
                else
                    statusList = null;
            }


            if (getArguments().getString(TAG_ISFIRST) != null) {
                isFirst = Boolean.parseBoolean(getArguments().getString(TAG_ISFIRST));
            }
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentDetailListComplainBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getActivity());
        binding.recyclerView.setLayoutManager(linearLayoutManager);
        binding.recyclerView.setHasFixedSize(true);

        if (statusList != null) {
            if (skeletonScreen != null)
                skeletonScreen.hide();

            if (!statusList.isEmpty()) {
                binding.cvTidaketemu.setVisibility(View.GONE);
                binding.recyclerView.setVisibility(View.VISIBLE);

                listComplainAdapter = new ListComplainAdapter(statusList, getActivity(), this);
                binding.recyclerView.setAdapter(listComplainAdapter);
            } else {
                binding.cvTidaketemu.setVisibility(View.VISIBLE);
                binding.recyclerView.setVisibility(View.GONE);
            }
        } else if (isFirst) {
            skeletonScreen = Skeleton.bind(binding.recyclerView)
                    .adapter(listComplainAdapter)
                    .shimmer(true)
                    .angle(20)
                    .frozen(false)
                    .duration(1200)
                    .count(5)
                    .load(R.layout.item_skeleton_list_complain)
                    .show();
        } else {
            if (skeletonScreen != null)
                skeletonScreen.hide();

            binding.cvTidaketemu.setVisibility(View.VISIBLE);
            binding.recyclerView.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onClickDetail(ListComplainResponse.Status status, int position) {
        onDetailClick.callback(status, position);
    }
}