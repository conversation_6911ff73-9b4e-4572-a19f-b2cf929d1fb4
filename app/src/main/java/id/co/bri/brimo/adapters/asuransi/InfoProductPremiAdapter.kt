package id.co.bri.brimo.adapters.asuransi

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.ListInfoJaminanAdapterBinding
import id.co.bri.brimo.databinding.ListInfoPremiAdapterBinding
import id.co.bri.brimo.models.apimodel.response.asuransi.DetailAsuransiResponse

class InfoProductPremiAdapter(private var context : Context, private var list : List<DetailAsuransiResponse.InformationSection.TitleSection.Header>):  RecyclerView.Adapter<InfoProductPremiAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ListInfoPremiAdapterBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InfoProductPremiAdapter.ViewHolder {
        val binding = ListInfoPremiAdapterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InfoProductPremiAdapter.ViewHolder, position: Int) {
        holder.binding.tvTitle.text = list[position].name
        holder.binding.tvDetail.text = list[position].value

    }

    override fun getItemCount(): Int {
        return list.size
    }
}