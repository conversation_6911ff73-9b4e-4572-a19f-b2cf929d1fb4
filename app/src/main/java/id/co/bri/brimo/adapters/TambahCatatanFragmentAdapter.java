package id.co.bri.brimo.adapters;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

public class TambahCatatanFragmentAdapter extends FragmentPagerAdapter {

    private static int NUM_OF_FRAGMENTS = 2;

    private final List<Fragment> mFragmentList;
    private final List<String> mTitle;
    private final Context context;

    public TambahCatatanFragmentAdapter(FragmentManager fm, List<Fragment> fragmentList, List<String> mTitle, Context context) {
        super(fm);
        this.mFragmentList = fragmentList;
        this.mTitle = mTitle;
        this.context = context;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mTitle.size();
    }

    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        return mTitle.get(position);
    }
}
