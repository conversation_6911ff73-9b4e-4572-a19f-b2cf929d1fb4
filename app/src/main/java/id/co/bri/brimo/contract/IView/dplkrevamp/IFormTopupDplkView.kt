package id.co.bri.brimo.contract.IView.dplkrevamp

import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse

interface IFormTopupDplkView: IBaseFormRevampView {
    fun onSuccessGetInquiry(inquiryRevampResponse: InquiryDompetDigitalResponse, urlConfirm: String, urlPayment: String)
    fun onSuccessGetHistoryDetailClaimDplk(data: ReceiptRevampResponse)

}