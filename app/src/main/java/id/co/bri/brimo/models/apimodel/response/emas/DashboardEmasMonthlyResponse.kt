package id.co.bri.brimo.models.apimodel.response.emas

import com.google.gson.annotations.Expose

import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.response.InfoResponse


class DashboardEmasMonthlyResponse {

    @SerializedName("monthly_activity")
    @Expose
    val monthlyActivity: MonthlyActivity? = null

    @SerializedName("content")
    @Expose
    val content: ContentResponse? = null

    @SerializedName("reference_number")
    @Expose
    val referenceNumber: String? = null


    class MonthlyActivity{
        @SerializedName("total_buy")
        @Expose
        val totalBuy: Float? = null

        @SerializedName("total_buy_string")
        @Expose
        val totalBuyString: String? = null

        @SerializedName("gold_buy")
        @Expose
        val goldBuy: Float? = null

        @SerializedName("gold_buy_string")
        @Expose
        val goldBuyString: String? = null

        @SerializedName("total_sell")
        @Expose
        val totalSell: Float? = null

        @SerializedName("total_sell_string")
        @Expose
        val totalSellString: String? = null

        @SerializedName("gold_sell")
        @Expose
        val goldSell: Float? = null

        @SerializedName("gold_sell_string")
        @Expose
        val goldSellString: String? = null

    }

}