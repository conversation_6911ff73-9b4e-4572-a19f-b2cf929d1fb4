package id.co.bri.brimo.ui.activities;

import static id.co.bri.brimo.domain.config.Constant.JourneyType.JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.pdam.IFormPdamPresenter;
import id.co.bri.brimo.contract.IView.pdam.IFormPdamView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.BankModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.PdamModel;
import id.co.bri.brimo.models.apimodel.request.InquiryPdamRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRequest;
import id.co.bri.brimo.models.apimodel.response.DataListBankResponse;
import id.co.bri.brimo.models.apimodel.response.DataListPdamResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.base.BaseFormActivity;

public class FormPdamActivity extends BaseFormActivity implements IFormPdamView {

    private static final String TAG = "FormPdam";

    protected List<PdamModel> pdamModelList;

    @Inject
    IFormPdamPresenter<IFormPdamView> presenter;

    public static void launchIntent(Activity caller, boolean fromFastMenu) {
        Intent intent = new Intent(caller, FormPdamActivity.class);
        isFromFastMenu = fromFastMenu;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        isShowButtonAddSavedList(true);
        llAddSavedList.setOnClickListener(view -> goToAddList(JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST));
    }

    @Override
    protected void injectDependency() {
        super.injectDependency();
        getActivityComponent().inject(this);

        if (presenter != null) {
            initiateUrlPresenter();

            if (isFromFastMenu) {
                presenter.getDataFormFastMenu();
            } else {
                presenter.getDataForm();
            }
        }
    }

    /**
     * method ini digunakan untuk inisiasi URL berdasarkan flag FastMenu
     */
    private void initiateUrlPresenter() {
        presenter.setView(this);

        presenter.setFormUrl(GeneralHelper.getString(R.string.url_pdam_form));
        presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_pdam_inq));
        presenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_pdam_conf));
        presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_pdam_pay));

        presenter.start();
    }

    @Override
    public void onClickSavedItem(SavedResponse savedResponse) {
        String s = savedResponse.getValue();
        str1 = s.split("\\|");
        String idArea = str1[1];
        String noPdam = str1[2];

        presenter.getDataInquiry(new InquiryPdamRequest(idArea, noPdam));
    }

    @Override
    public void onClickHistoryItem(HistoryResponse historyResponse) {
        String s = historyResponse.getValue();
        str1 = s.split("\\|");
        String idArea = str1[0];
        String noPdam = str1[1];

        presenter.getDataInquiry(new InquiryPdamRequest(idArea, noPdam));
    }

    @Override
    public void onSuccessUpdate(SavedResponse savedResponse, int item, int type) {
        Log.d(TAG, "onSuccessFavorite: update berhasil " + GeneralHelper.getString(R.array.type_option_desc, type));
        String message = GeneralHelper.getString(R.array.type_option_desc, type);
        showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false);
        presenter.getDataForm();
    }

    @Override
    public void onSuccessGetRestResponse(RestResponse restResponse) {
        DataListPdamResponse dataListPdamResponse = restResponse.getData(DataListPdamResponse.class);
        pdamModelList = dataListPdamResponse.getPdamModelList();
    }

    @Override
    public int getLayoutResource() {
        return R.layout.activity_form_briva;
    }

    @Override
    public int getDefaultIconResource() {
        return 0;
    }

    @Override
    public void setTextForm() {
        tvTerakhir.setText("Pembayaran Terakhir");
        tvDaftarTersimpan.setText("Daftar PDAM");
        btnSubmitTambah.setText("Pembayaran Baru");
    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.pdam_title_bar);
    }

    @Override
    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal Tagihan");
        parameterModel.setStringButtonSubmit("Bayar");
        parameterModel.setStringLabelMinimum("Bayar");
        parameterModel.setDefaultIcon(getDefaultIconResource());

        return parameterModel;
    }

    @Override
    public void onAddNewClick() {
        goToAddList("");
    }

    @Override
    public void goToAddList(String journeyType) {
        String urlInquiry = "";
        String urlKonfirmasi = "";
        String urlPayment = "";
        if (journeyType.equalsIgnoreCase(JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST)) {
            urlInquiry = GeneralHelper.getString(R.string.url_inquiry_add_saved_list_pdam);
            urlKonfirmasi = "";
            urlPayment = GeneralHelper.getString(R.string.url_inquiry_save_saved_list_pdam);
        } else {
            urlInquiry = GeneralHelper.getString(R.string.url_pdam_inq);
            urlKonfirmasi = GeneralHelper.getString(R.string.url_pdam_conf);
            urlPayment = GeneralHelper.getString(R.string.url_pdam_pay);
        }

        TambahDaftarPdamActivity.launchIntentAddSavedList(
                this,
                getTitleBar(),
                urlInquiry,
                urlKonfirmasi,
                urlPayment,
                pdamModelList,
                setParameter(),
                journeyType
        );
    }

    @Override
    public void onUpdateItem(SavedResponse savedResponseItem, int type, int position) {
        if (type == Constant.EditOption.FAV) {
            presenter.setUpdateItem(GeneralHelper.getString(R.string.url_pdam_favorit), savedResponseItem, position, type);
        }

        if (type == Constant.EditOption.HAPUS) {
            presenter.setUpdateItem(GeneralHelper.getString(R.string.url_pdam_delete), savedResponseItem, position, type);
        }

        if (type == Constant.EditOption.EDIT) {
            FormEditSavedActivity.launchIntent(this, savedResponseItem, position, getDefaultIconResource(), GeneralHelper.getString(R.string.url_pdam_update), "Nomor Tujuan");
        }

        if (type == Constant.EditOption.NON_FAV) {
            presenter.setUpdateItem(GeneralHelper.getString(R.string.url_pdam_unfavorit), savedResponseItem, position, Constant.EditOption.NON_FAV);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                String nama = data.getStringExtra(Constant.TAG_TITLE);
                int position = data.getIntExtra(Constant.TAG_POSITION, 0);
                handleEditOrSaveAction(Constant.EditOption.EDIT);
            }
        }

        if (requestCode == Constant.REQ_SAVE_SAVED) {
            if (resultCode == RESULT_OK) {
                handleEditOrSaveAction(Constant.EditOption.SAVE);
            } else if (resultCode == RESULT_CANCELED && data != null) {
                errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                cekErrorMessage();
            }
        }
    }

    private void handleEditOrSaveAction(int editOption) {
        String message = GeneralHelper.getString(R.array.type_option_desc_revamp, editOption);
        showSnackbarErrorMessage(message, BaseActivity.ALERT_CONFIRM, this, false);
        presenter.getDataForm();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (presenter != null) {
            initiateUrlPresenter();
        }
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        presenter.stop();
        super.onBackPressed();
    }
}