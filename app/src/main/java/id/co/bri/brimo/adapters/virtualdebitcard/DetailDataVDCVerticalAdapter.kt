package id.co.bri.brimo.adapters.virtualdebitcard

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.DetailDataVdcVerticalBinding
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailData

class DetailDataVDCVerticalAdapter :
    RecyclerView.Adapter<DetailDataVDCVerticalAdapter.DetailDataVDCVerticalHolder>() {
    var detailData = mutableListOf<DetailData>()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DetailDataVDCVerticalHolder {
        val binding =
            DetailDataVdcVerticalBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DetailDataVDCVerticalHolder(binding)
    }

    override fun getItemCount(): Int = detailData.size

    override fun onBindViewHolder(holder: DetailDataVDCVerticalHolder, position: Int) {
        holder.bindData(detailData[position])
    }

    inner class DetailDataVDCVerticalHolder(val binding: DetailDataVdcVerticalBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(detailData: DetailData) {
            binding.descriptionTxt.text = detailData.name
            binding.valueTxt.text = detailData.value
        }
    }
}