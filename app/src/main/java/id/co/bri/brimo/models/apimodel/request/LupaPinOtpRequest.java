package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class LupaPinOtpRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("otp")
    @Expose
    private String otp;

    public LupaPinOtpRequest(String referenceNumber, String otp) {
        this.referenceNumber = referenceNumber;
        this.otp = otp;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }
}
