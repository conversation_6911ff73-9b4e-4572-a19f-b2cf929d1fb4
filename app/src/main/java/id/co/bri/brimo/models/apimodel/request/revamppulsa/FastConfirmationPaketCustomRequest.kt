package id.co.bri.brimo.models.apimodel.request.revamppulsa

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.request.FastMenuRequest

class FastConfirmationPaketCustomRequest(
    fastMenuRequest: FastMenuRequest,
    @field:Expose @field:SerializedName("reference_number") var referenceNumber: String?,
    @field:Expose @field:SerializedName("phone_number") var phoneNumber: String?,
    @field:Expose @field:SerializedName("operator_id") var operatorId: String?,
    @field:Expose @field:SerializedName("save_as") var saveAs: String?,
    @field:Expose @field:SerializedName("item") var item: String?,
    @field:Expose @field:SerializedName("purchase_type") var purchaseType: String?,
    @field:Expose @field:SerializedName("note") var note: String?,
) : FastMenuRequest(fastMenuRequest.username, fastMenuRequest.tokenKey)