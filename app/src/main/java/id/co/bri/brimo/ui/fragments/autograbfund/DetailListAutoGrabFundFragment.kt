package id.co.bri.brimo.ui.fragments.autograbfund

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.autograbfund.SummaryItemListAgfAdapter
import id.co.bri.brimo.databinding.FragmentDetailListAgfBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.autograbfund.ListAutoGrabFundResponse
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity
import kotlin.properties.Delegates


class DetailListAutoGrabFundFragment :
    BottomSheetDialogFragment() {


    private val TAG = "DetailListAutoGrabFundFragment"

    private lateinit var binding: FragmentDetailListAgfBinding

    private var mOnClickDelete:((pos: Int)-> Unit)? = null
    private var mOnChangeStatus:((pos: Int, statusAgf: Boolean)-> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeMaterial)
    }


    companion object {
        private lateinit var mDetailAgf: ListAutoGrabFundResponse
        private lateinit var mContext: Context
        private var mPos by Delegates.notNull<Int>()
        @JvmStatic
        fun newInstance(detailAgf: ListAutoGrabFundResponse, pos: Int, context: Context) : DetailListAutoGrabFundFragment{
            val fragment = DetailListAutoGrabFundFragment()
            mDetailAgf = detailAgf
            mPos = pos
            mContext = context
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        (dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        // Inflate the layout for this fragment
        binding = FragmentDetailListAgfBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupView()
    }

    private fun setupView(){
        with(binding){
            btnClose.setOnClickListener { dismiss() }
            btnDelete.setOnClickListener { mOnClickDelete?.invoke(mPos) }
            setupSummary(mDetailAgf.details?.detailAgf ?: arrayListOf())
            setupNominal(mDetailAgf.details?.detailNominal ?: arrayListOf())
            binding.tvTitleAgf.text = mDetailAgf.product?.name
            binding.tvDescAgf.text = mDetailAgf.product?.description
            GeneralHelper.loadIconTransaction(
                mContext,
                mDetailAgf.product?.image,
                mDetailAgf.product?.imageName,
                binding.ivIconAgf,
                GeneralHelper.getImageId(mContext, "bri")
            )
            if (mDetailAgf.action.desc.isEmpty()){
                binding.tvDescActionStatus.visibility = View.GONE
            }else{
                binding.tvDescActionStatus.visibility = View.VISIBLE
                binding.tvDescActionStatus.text = mDetailAgf.action.desc
            }

            setupInfo()

            val icon = if(mDetailAgf.agfType.equals(Constant.AgfType.AUTOMATIC)) {Constant.IconAgfType.AUTOMATIC} else {Constant.IconAgfType.REMINDER}
            GeneralHelper.loadIconTransaction(
                mContext,
                "",
                icon,
                binding.ivTypeAgf,
                GeneralHelper.getImageId(mContext, icon)
            )
            binding.tvAgfType.text= mDetailAgf.action.text
            binding.switchStatus.isChecked = mDetailAgf.action.status
            binding.tvNoRek.text = mDetailAgf.details?.selectedAccount?.acoountString
            if (!mDetailAgf.details?.selectedAccount?.alias.isNullOrEmpty()) {
                binding.tvAliasRek.text = mDetailAgf.details?.selectedAccount?.alias
            } else {
                binding.tvAliasRek.text = mDetailAgf.details?.selectedAccount?.name
            }
            if (!mDetailAgf.details?.selectedAccount?.imagePath.isNullOrEmpty()) {
                GeneralHelper.loadImageUrl(
                    mContext,
                    mDetailAgf.details?.selectedAccount?.imagePath,
                    binding.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.ivIconRek.setImageResource(R.drawable.bri)
            }
            if (mDetailAgf.details?.selectedAccount == null){
                llSelectedAccount.visibility = View.GONE
            }else{
                llSelectedAccount.visibility = View.VISIBLE
            }

            binding.switchStatus.setOnCheckedChangeListener{_, isChecked ->
                    mOnChangeStatus?.invoke(mPos, isChecked)
            }
        }
    }

    private fun setupSummary(listSummary: MutableList<DataView>){
        val adapter = SummaryItemListAgfAdapter(listSummary, mContext, mPos)
        val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        binding.rvDetailSummary.layoutManager = layoutManager
        binding.rvDetailSummary.itemAnimator = DefaultItemAnimator()
        binding.rvDetailSummary.adapter = adapter
    }

    private fun setupNominal(listNominal: MutableList<DataView>){
        val adapter = SummaryItemListAgfAdapter(listNominal, mContext, mPos)
        val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        binding.rvDetailNominal.layoutManager = layoutManager
        binding.rvDetailNominal.itemAnimator = DefaultItemAnimator()
        binding.rvDetailNominal.adapter = adapter
    }

    private fun setupInfo(){
        if (mDetailAgf.details?.alertInfo != null && mDetailAgf.details?.alertInfo!!.isNotEmpty()){
            val ss = SpannableString(Html.fromHtml(mDetailAgf.details?.alertInfo))
            val span1: ClickableSpan = object : ClickableSpan() {
                override fun onClick(textView: View) {
                    SelfServiceActivity.launchIntent(activity)
                }
                override fun updateDrawState(textPaint: TextPaint) {
                    textPaint.color = GeneralHelper.getColor(activity, R.color.neutral_dark20)
                    textPaint.typeface = ResourcesCompat.getFont(mContext, R.font.bri_digital_text_bold)
                    textPaint.isUnderlineText = true
                }
            }

            ss.setSpan(span1, mDetailAgf.details?.alertInfo!!.indexOf("<b>"), mDetailAgf.details?.alertInfo!!.indexOf("</b>")-10, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.mcvInfo.visibility = View.VISIBLE
            binding.tvInfoDetail.text = ss
            binding.tvInfoDetail.movementMethod = LinkMovementMethod.getInstance()
        }else{
            binding.mcvInfo.visibility = View.GONE
        }

    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        return dialog
    }

    fun onClickDelete(listener:(pos: Int)->Unit){
        mOnClickDelete = listener
    }

    fun onChangeStatus(listener:(pos: Int, statusAgf: Boolean)->Unit){
        mOnChangeStatus = listener
    }
}