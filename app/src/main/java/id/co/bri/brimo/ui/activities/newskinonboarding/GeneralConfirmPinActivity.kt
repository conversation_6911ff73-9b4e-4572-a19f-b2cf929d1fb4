package id.co.bri.brimo.ui.activities.newskinonboarding

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityGeneralPinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.enums.PinEntryType
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.customviews.pinnewskin.PinEntryView

class GeneralConfirmPinActivity : NewSkinBaseActivity() {

    private lateinit var binding: ActivityGeneralPinBinding
    private lateinit var pinInputView: PinEntryView
    private var pinEntryType: PinEntryType = PinEntryType.FORGOT_PIN
    private var createdPin: String? = null

    companion object {
        const val EXTRA_PIN_ENTRY_TYPE = "extra_pin_entry_type"
        const val EXTRA_CREATED_PIN = "extra_created_pin"

        fun launchIntent(caller: Activity, refNumber: String?, pinEntryType: PinEntryType, createdPin: String?) {
            val intent = Intent(caller, GeneralConfirmPinActivity::class.java)
            intent.putExtra(Constant.REF_NUM, refNumber)
            intent.putExtra(EXTRA_PIN_ENTRY_TYPE, pinEntryType.name)
            intent.putExtra(EXTRA_CREATED_PIN, createdPin)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGeneralPinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        pinEntryType = PinEntryType.fromString(intent.getStringExtra(EXTRA_PIN_ENTRY_TYPE))
        createdPin = intent.getStringExtra(EXTRA_CREATED_PIN)

        setupPinView()
        updatePinTexts()
        setupForgotPinListener()
    }

//    override fun isScreenshotDisabled() = true

    private fun setupPinView() {
        pinInputView = PinEntryView(this).apply {
            descriptionText = ""
            infoText = ""
            infoTextColor = ContextCompat.getColor(context, R.color.black_ns_main)
            errorTextColor = ContextCompat.getColor(context, R.color.ns_red)
            infoTextTypeface = ResourcesCompat.getFont(context, R.font.bri_digital_text_regular)
            errorTextTypeface = ResourcesCompat.getFont(context, R.font.bri_digital_text_regular)

            onPinComplete = label@ { enteredPin ->
                if (enteredPin != createdPin) {
                    setErrorText("PIN salah")
                    return@label
                }
                when (pinEntryType) {
                    PinEntryType.FORGOT_PIN -> {
                        val intent = Intent(this@GeneralConfirmPinActivity, FastMenuNewSkinActivity::class.java)
                        intent.putExtra("show_snackbar", true)
                        startActivity(intent)
                        setResult(Activity.RESULT_OK)
                        finish()

                    }
                    PinEntryType.FORGOT_PASSWORD -> {

                    }
                    PinEntryType.CHANGE_PIN -> {
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                    PinEntryType.VALIDATE_PIN -> {

                    }
                    PinEntryType.VALIDATE_PASSWORD -> {
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                }
            }

            onPinError = { errorMessage ->
                setErrorText(errorMessage)
            }
        }

        binding.pinContainer.removeAllViews()
        binding.pinContainer.addView(pinInputView)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK, data)
            finish()
        }
    }

    private fun updatePinTexts() {
        when (pinEntryType) {
            PinEntryType.VALIDATE_PIN, PinEntryType.CHANGE_PIN -> {
                pinInputView.headerTitle = "Ubah PIN"
            }
            else -> {
                pinInputView.headerTitle = "PIN"
            }
        }
        pinInputView.descriptionText = "Konfirmasi PIN baru"
        pinInputView.infoText = "Silakan konfirmasi PIN yang sudah kamu buat"
        pinInputView.infoTextColor = ContextCompat.getColor(this, R.color.black_ns_main)
        pinInputView.isForgotPinVisible = false
    }

    private fun setupForgotPinListener() {
        pinInputView.setOnForgotPinClickListener {
            Toast.makeText(this, "Lupa PIN ditekan", Toast.LENGTH_SHORT).show()
        }
    }
}
