package id.co.bri.brimo.contract.IView.rdnrevamp

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnHistoryListWithdrawResponse

interface IHistoryWithdrawProcessRdnView: IMvpView {
    fun onSuccessGetHistoryWithdraw(response: RdnHistoryListWithdrawResponse)
    fun onSuccessGetHistoryWithdrawDetail(response: ReceiptRevampResponse)
    fun onExceptionTimeoutGateway(message: String)
}