package id.co.bri.brimo.models.apimodel.request.emas

import com.google.gson.annotations.SerializedName

class ConfirmationJualEmasRequest(
        @SerializedName("source_account" ) var sourceAccount : String?  = null,
        @SerializedName("sell_nominal"    ) var sellNominal    : Int?  = null,
        @SerializedName("sell_gram"        ) var sellGram        : Double?  = null,
        @SerializedName("sell_rate"     ) var sellRate     : Int?  = null,
)