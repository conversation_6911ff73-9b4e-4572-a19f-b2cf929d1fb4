package id.co.bri.brimo.models.apimodel.response.registrasi

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
class RegisBCFUResponse(
    @field:Expose @field:SerializedName("registration_id") var registrationid: String,
    @field:Expose @field:SerializedName("phone") var phone: String,
    @field:Expose @field:SerializedName("force_update") var forceUpdate: Boolean,
    @field:Expose @field:SerializedName("alternate_count") var alternateCount: Int,
    @field:Expose @field:SerializedName("alternate_phone") var alternatePhone: List<String> = emptyList()
) : Parcelable