package id.co.bri.brimo.ui.activities.ibbiz;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.RekeningIbbizAdapter;
import id.co.bri.brimo.contract.IPresenter.ibbiz.IRekeningIbbizPresenter;
import id.co.bri.brimo.contract.IView.ibbiz.IRekeningIbbizView;
import id.co.bri.brimo.databinding.ActivityRekeningIbbizBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.AccountIbbizResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class RekeningIbbizActivity extends BaseActivity implements
        IRekeningIbbizView,
        RekeningIbbizAdapter.OnClickItem {

    private ActivityRekeningIbbizBinding binding;

    protected static AccountIbbizResponse accountResponse;
    protected RekeningIbbizAdapter rekeningAdapter;

    protected String sAccount;

    @Inject
    IRekeningIbbizPresenter<IRekeningIbbizView> presenter;

    public static void launchIntent(Activity caller, AccountIbbizResponse accountIbbizResponse) {
        Intent intent = new Intent(caller, RekeningIbbizActivity.class);
        accountResponse = accountIbbizResponse;
        caller.startActivityForResult(intent, Constant.REQ_REGIS);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRekeningIbbizBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_qlola));

        injectDependency();
        setupViews();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlStatus(GeneralHelper.getString(R.string.url_ibbiz_inquiry));
        }
    }

    private void setupViews() {
        binding.recyclerview.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        rekeningAdapter = new RekeningIbbizAdapter(this, accountResponse.getAccountList(), this::clickItem);
        binding.recyclerview.setAdapter(rekeningAdapter);
    }

    @Override
    public void clickItem(String account) {
        sAccount = account;
        presenter.sendStatusAccount(account);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_CANCELED && data != null) {
            this.setResult(RESULT_CANCELED, data);
            this.finish();
        }
    }

    @Override
    public void onSuccess() {
        DataPerusahaanActivity.launchIntent(this, accountResponse.getPhone(), accountResponse.getEmail(), sAccount);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}