package id.co.bri.brimo.contract.IView.cashback;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.SelectCashbackResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse;

public interface ICashbackAllView extends IMvpView {
    void onSuccessSelectCashback(SelectCashbackResponse response);
    void onException12(String message);
    void onException93(String message);

    void onSuccessDashboardLifestyleMenu(DashboardLifestyleMenuResponse dashboardLifestyleMenuResponse);

}
