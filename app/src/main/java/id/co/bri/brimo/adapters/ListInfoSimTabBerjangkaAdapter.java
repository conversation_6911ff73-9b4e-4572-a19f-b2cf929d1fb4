package id.co.bri.brimo.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ListItemTabunganBerjangkaBinding;
import id.co.bri.brimo.models.apimodel.response.InfoSimpedesResponse;


public class ListInfoSimTabBerjangkaAdapter extends RecyclerView.Adapter<ListInfoSimTabBerjangkaAdapter.MyHolder> {

    protected Context context;
    protected List<InfoSimpedesResponse.ChildAccount> infoResponseList;
    protected int lastPosition = -1;
    protected OnCLickItem onCLickItem;

    public interface OnCLickItem {
        void clickItem(InfoSimpedesResponse.ChildAccount account);
    }

    public ListInfoSimTabBerjangkaAdapter(Context context, List<InfoSimpedesResponse.ChildAccount> infoResponseList, OnCLickItem onCLickItem) {
        this.context = context;
        this.infoResponseList = infoResponseList;
        this.onCLickItem = onCLickItem;
    }

    @NonNull
    @Override
    public MyHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyHolder(ListItemTabunganBerjangkaBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull MyHolder holder, int position) {
        holder.binding.tvTitle.setText(infoResponseList.get(position).getDreamName());
        holder.binding.tvDesc.setText(infoResponseList.get(position).getAvailableBalance());
        holder.itemView.setOnClickListener(v -> onCLickItem.clickItem(infoResponseList.get(position)));
        setAnimation(holder.itemView, position);
    }

    private void setAnimation(View viewToAnimate, int position) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (position > lastPosition) {
            Animation animation = AnimationUtils.loadAnimation(context, R.anim.fade_in_button);
            animation.setStartOffset((long) lastPosition * 150);
            viewToAnimate.startAnimation(animation);
            lastPosition = position;
        }
    }

    @Override
    public int getItemCount() {
        return (infoResponseList != null) ? infoResponseList.size() : 0;
    }

    public static class MyHolder extends RecyclerView.ViewHolder {
        ListItemTabunganBerjangkaBinding binding;

        public MyHolder(@NonNull ListItemTabunganBerjangkaBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
