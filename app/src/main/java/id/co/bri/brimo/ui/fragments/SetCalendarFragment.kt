package id.co.bri.brimo.ui.fragments

import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.kizitonwose.calendarview.model.CalendarDay
import com.kizitonwose.calendarview.model.DayOwner
import com.kizitonwose.calendarview.ui.DayBinder
import com.kizitonwose.calendarview.ui.ViewContainer
import com.kizitonwose.calendarview.utils.next
import com.kizitonwose.calendarview.utils.previous
import com.kizitonwose.calendarview.utils.yearMonth
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentSetCalendarBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.daysOfWeekFromLocale
import id.co.bri.brimo.domain.helpers.calendar.makeInVisible
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.helpers.calendar.setTextColorRes
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.YearMonth

class SetCalendarFragment(onSelectDate: OnSelectDate) : BottomSheetDialogFragment() {

    private var _binding: FragmentSetCalendarBinding? = null
    private val binding get() = _binding!!
    private val TAG = "SetCalendarFragment"

    private val today = LocalDate.now()
    private val monthTitleFormatter = org.threeten.bp.format.DateTimeFormatter.ofPattern("MMMM")

    private var tempDate: LocalDate? = null
    private var startDate: LocalDate? = null
    private var endDate: LocalDate? = null
    private var startDateString: String? = null
    private var endDateString: String? = null

    private var selectDate: OnSelectDate? = onSelectDate

    lateinit var daysOfWeek: Array<DayOfWeek>

    private var multipleDays = false

    private var getStartDate = false
    private var getEndDate = false
    private var maxToday = false
    private var debetDate = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    interface OnSelectDate {
        fun onSelect(dateSelect: LocalDate)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSetCalendarBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val bundle: Bundle? = arguments
        multipleDays = bundle?.getBoolean(Constant.TAG_PICK_DATE) ?: false
        getStartDate = bundle?.getBoolean(Constant.TAG_PICK_START_DATE) ?: false
        getEndDate = bundle?.getBoolean(Constant.TAG_PICK_END_DATE) ?: false
        maxToday = bundle?.getBoolean(Constant.TAG_MAX_TODAY) ?: false
        startDateString = bundle?.getString(Constant.TAG_START_DATE).orEmpty()
        endDateString = bundle?.getString(Constant.TAG_END_DATE).orEmpty()
        debetDate = bundle?.getBoolean(Constant.TAG_DEBET_DATE) ?: false

        daysOfWeek = daysOfWeekFromLocale()

        // set current Start Date
        if (startDateString != null && !startDateString.equals("")) {
            try {
                startDate = LocalDate.parse(startDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startDate LocalDate.parse: ", e)
                }
            }
        }

        // set current End Date
        if (endDateString != null && !endDateString.equals("")) {
            try {
                endDate = LocalDate.parse(endDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "endDate LocalDate.parse: ", e)
                }
            }
        }

        setupCalendar()
        setupButton()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.setOnShowListener {

            val bottomSheetDialog = it as BottomSheetDialog
            val parentLayout =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            parentLayout?.let { it ->
                val behaviour = BottomSheetBehavior.from(it)
                setupFullHeight(it)
                behaviour.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }
        return dialog
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    private fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
    }

    private fun setupButton() {
        binding.ivBack.setOnClickListener {
            dismiss()
        }

        binding.btPilihDurasi.text = resources.getString(R.string.hinttanggal)

        binding.btPilihDurasi.setOnClickListener {
            val startDate = startDate
            val endDate = endDate

            if (getStartDate || getEndDate) {
                if (startDate == null) {
                    if (endDate != null)
                        returnEndDatePicked(endDate)
                    else
                        Toast.makeText(
                            activity,
                            GeneralHelper.getString(R.string.please_select_a_date_first),
                            Toast.LENGTH_SHORT
                        ).show()
                } else if (endDate == null) {
                    if (startDate != null)
                        returnSingleDatePicked(startDate)
                    else
                        Toast.makeText(
                            activity,
                            GeneralHelper.getString(R.string.please_select_a_date_first),
                            Toast.LENGTH_SHORT
                        ).show()
                }
            } else if (startDate != null) {
                returnSingleDatePicked(startDate)
            } else {
                Toast.makeText(
                    activity,
                    GeneralHelper.getString(R.string.please_select_a_date_first),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun returnSingleDatePicked(startDate: LocalDate) {
        selectDate?.onSelect(startDate)
        dismiss()
    }

    private fun returnEndDatePicked(endDate: LocalDate) { }


    private fun setupCalendar() {
        setupCalendarView()
        setupCalendarNavigation()
    }

    private fun setupCalendarNavigation() {
        binding.ibPreviousMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.previous)
            }
        }

        binding.ibNextMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.next)
            }
        }
    }

    private fun setupCalendarView() {
        setupMonth()
        setupCalendarBinder()
    }

    private fun setupCalendarBinder() {
        class DayViewContainer(view: View) : ViewContainer(view) {
            // Will be set when this container is bound. See the dayBinder.
            lateinit var day: CalendarDay
            val textView = view.findViewById<TextView>(R.id.tvCalendarDay)
            val roundBgView = view.findViewById<View>(R.id.calendarDayView)
            val leftGreyArea = view.findViewById<View>(R.id.leftGreyArea)
            val rightGreyArea = view.findViewById<View>(R.id.rightGreyArea)

            init {
                view.setOnClickListener {
                    if (day.owner == DayOwner.THIS_MONTH && (day.date == today || day.date.isBefore(
                            today
                        ) || multipleDays || debetDate)
                    ) {
                        val date = day.date
                        if (multipleDays) {
                            //untuk multiple days
                            if (startDate != null) {
                                if (date < startDate || endDate != null) {
                                    if (maxToday && date > today) {
                                        startDate = today
                                    } else {
                                        startDate = date
                                    }
                                    endDate = null
                                } else if (date != startDate) {
                                    if (maxToday && date > today) {
                                        endDate = today
                                    } else {
                                        endDate = date
                                    }
                                }
                            } else {
                                startDate = date
                            }
                        } else {
                            if (getStartDate) {
                                if (endDate != null) {
                                    if (date > endDate) {
                                        tempDate = endDate
                                        endDate = date
                                        startDate = tempDate
                                    } else {
                                        startDate = date
                                    }
                                } else {
                                    startDate = date
                                }
                            } else if (getEndDate) {
                                if (startDate != null) {
                                    if (date < startDate) {
                                        tempDate = startDate
                                        startDate = date
                                        endDate = tempDate
                                    } else {
                                        endDate = date
                                    }
                                } else {
                                    endDate = date
                                }
                            } else {
                                //khusus single days
                                startDate = date
                                endDate = date
                            }

                        }

                        binding.calendarView.notifyCalendarChanged()
//                        bindSummaryViews()
                    }
                }
            }
        }

        setupScrollListener()

        binding.calendarView.dayBinder = object : DayBinder<DayViewContainer> {
            override fun create(view: View): DayViewContainer {
                return DayViewContainer(view)
            }

            override fun bind(container: DayViewContainer, day: CalendarDay) {
                container.day = day
                val textView = container.textView
                val roundBgView = container.roundBgView
                val leftGreyArea = container.leftGreyArea
                val rightGreyArea = container.rightGreyArea

                //Make default container to be empty before binding the dates in
                textView.text = null
                textView.background = null
                roundBgView.makeInVisible()
                leftGreyArea.makeInVisible()
                rightGreyArea.makeInVisible()

                //Set date to container which correctly are dates of corresponding month
                if (day.owner == DayOwner.THIS_MONTH) {
                    textView.text = day.day.toString()

                    //Disable date after today's date
                    if (day.date.isAfter(today) && maxToday) {
                        textView.setTextColorRes(R.color.colorAbu2)
                    } else if (day.date.isBefore(today.minusDays(-1)) && debetDate || day.date.isAfter(
                            today.plusDays(27)
                        ) && debetDate
                    ) {
                        textView.setTextColorRes(R.color.colorAbu2)
                    } else {
                        when {
                            //jika tanggal sama
                            startDate == day.date && endDate == day.date -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                leftGreyArea.makeInVisible()
                                rightGreyArea.makeInVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }
                            // If only one day picked startDate
                            startDate == day.date && endDate == null -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }
                            // If only one day picked endDate
                            startDate == null && endDate == day.date -> {
                                textView.setTextColorRes(R.color.white)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
                            }

                            // If date range has been picked, set start date background to provide half oval shape
                            day.date == startDate -> {
                                textView.setTextColorRes(R.color.white)
                                if (multipleDays || getEndDate || getStartDate) {
                                    rightGreyArea.makeVisible()
                                }

                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
//                                updateDrawableRadius(textView)
//                                textView.background = startBackground
                            }
                            // If date range has been picked, set background of date between start date and end date to grey area
                            startDate != null && endDate != null && (day.date > startDate && day.date < endDate) -> {
                                textView.setTextColorRes(R.color.colorBlack)
                                textView.setBackgroundResource(R.drawable.example_4_continuous_selected_bg_middle)
                            }
                            // If date range has been picked, set end date background to provide half oval shape
                            day.date == endDate -> {
                                textView.setTextColorRes(R.color.white)
                                if (multipleDays || getEndDate || getStartDate) {
                                    leftGreyArea.makeVisible()
                                }

                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
//                                updateDrawableRadius(textView)
//                                textView.background = endBackground
                            }
                            // Add circle to today's date

                            day.date == today && !debetDate -> {
                                textView.setTextColorRes(R.color.colorButtonOrange)
                                roundBgView.makeVisible()
                                roundBgView.setBackgroundResource(R.drawable.example_4_today_bg)
                            }

                            day.date >= today && !multipleDays && !debetDate -> {
                                textView.setTextColorRes(R.color.hintblack)
                            }

                            day.date <= today && !multipleDays && debetDate -> {
                                textView.setTextColorRes(R.color.hintblack)
                            }

                            day.date >= today.plusDays(28) && !multipleDays && debetDate -> {
                                textView.setTextColorRes(R.color.hintblack)
                            }

                            else -> textView.setTextColorRes(R.color.colorBlack)

                        }
                    }
                } else {

                    // This part is to make the coloured selection background continuous
                    // on the blank in and out dates across various months and also on dates(months)
                    // between the start and end dates if the selection spans across multiple months.

                    val startDate = startDate
                    val endDate = endDate
                    if (startDate != null && endDate != null) {
                        // Mimic selection of inDates that are less than the startDate.
                        // Example: When 26 Feb 2019 is startDate and 5 Mar 2019 is endDate,
                        // this makes the inDates in Mar 2019 for 24 & 25 Feb 2019 look selected.
                        if ((day.owner == DayOwner.PREVIOUS_MONTH
                                    && startDate.monthValue == day.date.monthValue
                                    && endDate.monthValue != day.date.monthValue) ||
                            // Mimic selection of outDates that are greater than the endDate.
                            // Example: When 25 Apr 2019 is startDate and 2 May 2019 is endDate,
                            // this makes the outDates in Apr 2019 for 3 & 4 May 2019 look selected.
                            (day.owner == DayOwner.NEXT_MONTH
                                    && startDate.monthValue != day.date.monthValue
                                    && endDate.monthValue == day.date.monthValue) ||

                            // Mimic selection of in and out dates of intermediate
                            // months if the selection spans across multiple months.
                            (startDate < day.date && endDate > day.date
                                    && startDate.monthValue != day.date.monthValue
                                    && endDate.monthValue != day.date.monthValue)
                        ) {
                            textView.setBackgroundResource(R.drawable.example_4_continuous_selected_bg_middle)
                        }
                    }

                }
            }
        }
    }

    /**
     * Setup scroll listener of calendar view and change the month-year display according to its adapter position
     */
    private fun setupScrollListener() {
        binding.calendarView.monthScrollListener = { month ->
            val title = "${monthTitleFormatter.format(month.yearMonth)} ${month.yearMonth.year}"
            binding.tvMonthYearFrag.text = title
        }
    }

    private fun setupMonth() {
        var totalPreviousMonthsDisplayed: Long = 12
        var totalNextMonthsDisplayed: Long = 0
        val currentMonth = YearMonth.now()
        val startDate = startDate
        val endDate = endDate

        if (maxToday) {
            totalNextMonthsDisplayed = 0
        }

        if (debetDate) {
            totalNextMonthsDisplayed = 1
            totalPreviousMonthsDisplayed = 0
        }

        val startMonth = currentMonth.minusMonths(totalPreviousMonthsDisplayed)
        val endMonth = currentMonth.plusMonths(totalNextMonthsDisplayed)

        binding.calendarView.setup(startMonth, endMonth, daysOfWeek.first())

        if (getStartDate && startDate != null) {
            val starMonth = startDate.yearMonth
            binding.calendarView.scrollToMonth(starMonth)
        } else if (getEndDate && endDate != null) {
            val endMonth = endDate.yearMonth
            binding.calendarView.scrollToMonth(endMonth)
        } else {
            binding.calendarView.scrollToMonth(currentMonth)
        }
    }
}