package id.co.bri.brimo.ui.activities.britamarencana;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import org.threeten.bp.LocalDate;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BillingAmountAdapter;
import id.co.bri.brimo.adapters.DetailTransaksiAdapter;
import id.co.bri.brimo.contract.IPresenter.britamarencana.IKonfirmasiRencanaPresenter;
import id.co.bri.brimo.contract.IView.britamarencana.IKonfirmasiRencanaView;
import id.co.bri.brimo.databinding.ActivityKonfirmasiRencanaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiEditRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiRencanaRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryEditRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiRencanaResponse;
import id.co.bri.brimo.ui.activities.SetCalendarActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.SetCalendarFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

public class KonfirmasiRencanaActivity extends BaseActivity implements
        View.OnClickListener,
        IKonfirmasiRencanaView,
        SumberDanaFragment.SelectSumberDanaInterface,
        SetCalendarFragment.OnSelectDate {

    private ActivityKonfirmasiRencanaBinding binding;

    protected static final String TAG_RESPONSE = "response";
    protected static final String TAG_ISS3F = "isS3f";

    BillingAmountAdapter amountAdapter;
    DetailTransaksiAdapter detailTransaksiAdapter;
    AccountModel model;
    private List<AccountModel> mListAccountModel;
    private List<Integer> mListFailed;

    int counter = 0;
    String akunDefault;
    Double saldo = 0.0;
    String msaldoString = "";

    private DurasiModel durasiModel = null;
    protected String tanggal = "";
    protected String tanggalString = "";
    protected static String productTypes;
    protected static InquiryOpenRencanaResponse mInquiryOpenRencanaResponse;
    protected static InquiryEditRencanaResponse mInquiryEditRencanaResponse;

    protected static boolean isFromUbah = false;
    protected boolean isS3F = false;

    public static void launchIntent(Activity caller, InquiryOpenRencanaResponse inquiryOpenRencanaResponse, boolean fromUbah, String productType) {
        Intent intent = new Intent(caller, KonfirmasiRencanaActivity.class);
        mInquiryOpenRencanaResponse = inquiryOpenRencanaResponse;
        isFromUbah = fromUbah;
        productTypes = productType;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, InquiryEditRencanaResponse inquiryEditRencanaResponse, boolean fromUbah, String productType) {
        Intent intent = new Intent(caller, KonfirmasiRencanaActivity.class);
        isFromUbah = fromUbah;
        mInquiryEditRencanaResponse = inquiryEditRencanaResponse;
        productTypes = productType;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Inject
    IKonfirmasiRencanaPresenter<IKonfirmasiRencanaView> presenter;

    public static void launchIntentSimpedes3Fungsi(Activity caller, InquiryOpenRencanaResponse inquiryOpenRencanaResponse, boolean isS3F) {
        Intent intent = new Intent(caller, KonfirmasiRencanaActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(inquiryOpenRencanaResponse));
        intent.putExtra(TAG_ISS3F, String.valueOf(isS3F));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityKonfirmasiRencanaBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        if (getIntent().getExtras() != null) {
            if (getIntent().getExtras().getString(TAG_RESPONSE) != null) {
                mInquiryOpenRencanaResponse = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), InquiryOpenRencanaResponse.class);
            }
            if (getIntent().getExtras().getString(TAG_ISS3F) != null) {
                isS3F = Boolean.parseBoolean(getIntent().getExtras().getString(TAG_ISS3F));
            }
        }

        if (isS3F)
            GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_simpedes));
        else
            GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "BritAma Rencana");

        injectDependency();
        setView();
        if (!isFromUbah) setupAccount();
    }

    public void setView() {
        editListener();
        binding.etdebetdate.setOnClickListener(this);
        binding.tvrecount.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
        binding.btnSubmit2.setOnClickListener(this);
        binding.itemLayoutBackground.setOnClickListener(this);

        if (isFromUbah) {
            binding.tvtotal.setText(mInquiryEditRencanaResponse.getTargetAmountString());

            //Detail target dan bulan
            binding.rvDetailPayment.setHasFixedSize(true);
            binding.rvDetailPayment.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            detailTransaksiAdapter = new DetailTransaksiAdapter(mInquiryEditRencanaResponse.getBillingDetail(), this);
            binding.rvDetailPayment.setAdapter(detailTransaksiAdapter);

            //Total tagihan per bulan
            binding.rvBillingAmount.setHasFixedSize(true);
            binding.rvBillingAmount.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
            amountAdapter = new BillingAmountAdapter(this, mInquiryEditRencanaResponse.getBillingAmount());
            binding.rvBillingAmount.setAdapter(amountAdapter);

            binding.content.setBackgroundColor(getResources().getColor(R.color.toolbar_blue));
            binding.btn1.setVisibility(View.GONE);
            binding.btn2.setVisibility(View.VISIBLE);
            binding.layoutkonfirmasi.setVisibility(View.GONE);
        } else {
            binding.tvtotal.setText(mInquiryOpenRencanaResponse.getTargetAmountString());

            //Detail target dan bulan
            binding.rvDetailPayment.setHasFixedSize(true);
            binding.rvDetailPayment.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            detailTransaksiAdapter = new DetailTransaksiAdapter(mInquiryOpenRencanaResponse.getBillingDetail(), this);
            binding.rvDetailPayment.setAdapter(detailTransaksiAdapter);

            //Total tagihan per bulan
            binding.rvBillingAmount.setHasFixedSize(true);
            binding.rvBillingAmount.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
            amountAdapter = new BillingAmountAdapter(this, mInquiryOpenRencanaResponse.getBillingAmount());
            binding.rvBillingAmount.setAdapter(amountAdapter);

            binding.content.setBackgroundColor(getResources().getColor(R.color.colorButtonGrey));
            binding.btn1.setVisibility(View.VISIBLE);
            binding.btn2.setVisibility(View.GONE);
            binding.layoutkonfirmasi.setVisibility(View.VISIBLE);
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            if (isS3F)
                presenter.setUrl(GeneralHelper.getString(R.string.url_s3f_add_impian_confirmation));
            else if (isFromUbah)
                presenter.setUrl(GeneralHelper.getString(R.string.url_konfirmasi_ubah_rencana));
            else presenter.setUrl(GeneralHelper.getString(R.string.url_konfirmasi_open_rencana));
            presenter.start();
        }
    }

    public void editListener() {
        binding.etdebetdate.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!binding.etdebetdate.getText().toString().equals(GeneralHelper.getString(R.string.pilih_tanggal_auto_debet))) {
                    binding.btnSubmit.setAlpha(1);
                    binding.btnSubmit.setEnabled(true);
                }
            }
        });
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.etdebetdate:
                if (isS3F) {
                    SetCalendarFragment calendarFragment;
                    calendarFragment = new SetCalendarFragment(this::onSelect);
                    Bundle args = new Bundle();
                    args.putBoolean(Constant.TAG_DEBET_DATE, true);
                    calendarFragment.setArguments(args);

                    calendarFragment.setCancelable(true);
                    calendarFragment.show(getSupportFragmentManager(), "");
                } else {
                    Intent intent = new Intent(this, SetCalendarActivity.class);
                    intent.putExtra(Constant.TAG_DEBET_DATE, true);
                    startActivityForResult(intent, Constant.REQ_CALENDAR);
                }
                break;
            case R.id.tvrecount:
                finish();
                break;
            case R.id.btnSubmit:
            case R.id.btnSubmit2:
                if (isFromUbah)
                    presenter.getKonfirmasiEditRencana(new KonfirmasiEditRequest(mInquiryEditRencanaResponse.getReferenceNumber()));
                else
                    presenter.getKonfirmasiRencana(new KonfirmasiRencanaRequest(mInquiryOpenRencanaResponse.getReferenceNumber(), model.getAcoount(), tanggal));
                break;
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    if (isS3F) {
                        SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                    } else {
                        ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), "");
                    }
                }
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_CALENDAR) {
            if (resultCode == Activity.RESULT_OK) {
                if (data != null) {
                    durasiModel = new DurasiModel(
                            data.getIntExtra(Constant.START_DAY, 0),
                            data.getIntExtra(Constant.START_MONTH, 0),
                            data.getIntExtra(Constant.START_YEAR, 0));
                    setDurasi(durasiModel);
                }
            }
        }

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
            } else {
                this.setResult(RESULT_CANCELED, data);
            }
            this.finish();
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }

    }

    public void setDurasi(DurasiModel durasi) {
        this.durasiModel = durasi;
        if (isS3F) {
            String tanggalString = durasiModel.getStartDatePfmStringddMMyyyy().substring(0, 3) +
                    CalendarHelper.convertMont(durasiModel.getStartDatePfmStringddMMyyyy().substring(3, 5)) +
                    durasiModel.getStartDatePfmStringddMMyyyy().substring(5, 10);

            binding.etdebetdate.setText(tanggalString);
            tanggal = durasiModel.getStartDateString();
        } else {
            tanggalString = durasiModel.getStartDatePfmString();
            binding.etdebetdate.setText(tanggalString);
            tanggal = durasiModel.getStartDateString();
        }
    }

    protected void setupAccount() {
        //List Account

        model = new AccountModel();
        if (mInquiryOpenRencanaResponse.getAccountList().size() > 0) {
            mListAccountModel = mInquiryOpenRencanaResponse.getAccountList();
        }

        //get account default
        if (mListAccountModel != null) {
            for (AccountModel accountModel : mListAccountModel) {
                if (accountModel.getIsDefault() == 1) {
                    model = accountModel;
                    break;
                } else {
                    model = mListAccountModel.get(0);
                }
            }
        }

        if (model.getAcoountString() != null) {
            binding.tvNorek.setText(model.getAcoountString());
        }

        if (model.getName() != null) {
            binding.tvInisial.setText(GeneralHelper.formatInitialName(model.getName()));
        }

    }

    public void validasiButton() {
        if (saldo < 0) {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        } else {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }

    }

    @Override
    public void setDefaultSaldo(double defaultSaldo, String saldoString, String defaultAkun) {
        msaldoString = saldoString;
        saldo = defaultSaldo;
        akunDefault = defaultAkun;
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();
        if (isS3F) {
            parameterKonfirmasiModel.setDefaultIcon(GeneralHelper.getImageId(this, "ic_simpedes_bisa"));
        } else {
            parameterKonfirmasiModel.setDefaultIcon(GeneralHelper.getImageId(this, "bri"));
        }

        return parameterKonfirmasiModel;
    }

    @Override
    public void onSuccessKonfirmasiRencana(KonfirmasiRencanaResponse konfirmasiRencanaResponse) {
        if (isS3F)
            KonfirmasiDataRencanaActivity.launchIntent(this, isFromUbah, isS3F, true, konfirmasiRencanaResponse, setParameterKonfirmasi());
        else
            KonfirmasiDataRencanaActivity.launchIntent(this, isFromUbah, isS3F, false, konfirmasiRencanaResponse, setParameterKonfirmasi());
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;
        binding.tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));
        binding.tvNorek.setText(model.getAcoountString());
    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    public void onSelect(@NonNull LocalDate dateSelect) {
        durasiModel = new DurasiModel(
                dateSelect.getDayOfMonth(),
                dateSelect.getMonthValue(),
                dateSelect.getYear());
        setDurasi(durasiModel);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}