package id.co.bri.brimo.adapters.loaninapp

import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemFragmentLoanOnAppCcLandingPageSchemeBinding
import id.co.bri.brimo.models.loaninapp.LoanInAppOnboardingModel
import id.co.bri.brimo.util.extension.layInflater
import id.co.bri.brimo.util.recyclerview.BaseRecyclerViewFilterAdapter

class LoanInAppSchemeBottomAdapter : BaseRecyclerViewFilterAdapter<LoanInAppOnboardingModel.Scheme>() {
    override fun onCreateView(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return LoanInAppSchemeBottomAdapterHolder(ItemFragmentLoanOnAppCcLandingPageSchemeBinding.inflate(parent.layInflater(), parent, false))
    }

    override fun onBindView(data: LoanInAppOnboardingModel.Scheme, position: Int, holder: RecyclerView.ViewHolder) {
        when (holder) {
            is LoanInAppSchemeBottomAdapterHolder -> holder.setContent(data)
        }
    }

    inner class LoanInAppSchemeBottomAdapterHolder(private val binding: ItemFragmentLoanOnAppCcLandingPageSchemeBinding) : RecyclerView.ViewHolder(binding.root) {
        fun setContent(data: LoanInAppOnboardingModel.Scheme) = with(binding) {
            tvItemFragmentLoanOnAppCcLandingPageSchemeTenor.text = data.tenor
            tvItemFragmentLoanOnAppCcLandingPageSchemePercent.text = data.bunga
            tvItemFragmentLoanOnAppCcLandingPageSchemeMinimalInfo.text = data.subtitleTransaksi
            tvItemFragmentLoanOnAppCcLandingPageSchemeMinimalNominalTransaction.text = data.nominalTransaksi
            root.background = ContextCompat.getDrawable(root.context, if (absoluteAdapterPosition % 2 == 0) R.color.neutral_light10 else R.color.white)
        }
    }
}
