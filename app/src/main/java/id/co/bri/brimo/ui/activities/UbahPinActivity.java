package id.co.bri.brimo.ui.activities;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.PinNumberAdapter;
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter;
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapter;
import id.co.bri.brimo.contract.IPresenter.ubahpin.IUbahPinPresenter;
import id.co.bri.brimo.contract.IView.ubahpin.IUbahPinView;
import id.co.bri.brimo.databinding.ActivityUbahPinBinding;
import id.co.bri.brimo.di.modules.fragment.PinAllModule;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.ErrorResponseNewSkin;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers;

public class UbahPinActivity extends BaseActivity implements
        PinNumberAdapter.OnPinNumberListener,
        BasePinAdapter.PinAdapterListener,
        IUbahPinView {

    private ActivityUbahPinBinding binding;

    private OtpInputAdapter otpInputAdapter;

    @Inject
    IUbahPinPresenter<IUbahPinView> ubahPinPresenter;

    public static final int SESSION_EXP_CODE = 99;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, UbahPinActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUbahPinBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        setupLayout();

    }

    private void setupLayout() {
        GridLayoutManager pinPadLayoutManager;
        GeneralHelper.setToolbar(this, binding.toolbarPin.toolbar, GeneralHelper.getString(R.string.change_pin));

        otpInputAdapter = new OtpInputAdapter(this);
        PinNumberAdapter pinNumberAdapter = new PinNumberAdapter(InsertPinNumbers.Companion.getPinNumberList(this));
        GridLayoutManager pinOtpLayoutManager = new GridLayoutManager(this, 6);
        pinPadLayoutManager = new GridLayoutManager(this, 3);

        pinNumberAdapter.setOnPinNumberListener(this);
        otpInputAdapter.setListener(this);
        binding.tvLupaPin.setOnClickListener(v -> LupaPinActivity.launchIntent(this));

        binding.rvBox.setLayoutManager(pinOtpLayoutManager);
        binding.rvBox.setAdapter(otpInputAdapter);

        binding.rvInput.setLayoutManager(pinPadLayoutManager);
        binding.rvInput.setAdapter(pinNumberAdapter);
    }

    private void injectDependency() {
        getActivityComponent()
                .plusPinComponent(new PinAllModule())
                .inject(this);
        if (ubahPinPresenter != null) {
            ubahPinPresenter.setView(this);
            ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.url_ubah_pin_check));
            ubahPinPresenter.start();
        }
    }

    @Override
    public void onPinClicked(int pinNumber) {
        otpInputAdapter.addPin(String.valueOf(pinNumber));
    }

    @Override
    public void onDeleteClicked() {
        otpInputAdapter.deletePin();
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void notifyChanges() {
        otpInputAdapter.notifyDataSetChanged();
    }

    @Override
    public void onComplete(@NonNull String string) {
        ubahPinPresenter.setPin(string);
        ubahPinPresenter.ubahPin();
    }

    @Override
    public void onSuccess(String refNumber) {
        UbahPinBaruActivity.launchIntent(this, refNumber);
    }

    @Override
    public void onException50(String msg) {
        otpInputAdapter.deleteAllPin();
        LoginActivity.launchIntentWithDialog(this, msg);
        this.finish();
    }

    @Override
    public void resetInputPin() {
        otpInputAdapter.deleteAllPin();
    }

    @Override
    public void onErrorPin(String desc) {
        //do nothing
    }

    @Override
    public void onExceptionErrorAttemps(ErrorResponseNewSkin resp) {

    }

    @Override
    public void onErrorBlock(ErrorResponseNewSkin resp) {

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK, data);
            finish();
        }
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == UbahPinActivity.SESSION_EXP_CODE) {
            if (data != null && data.hasExtra(Constant.DESCRIPTION)) {
                onException(data.getStringExtra(Constant.DESCRIPTION));
            }
        }
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_CANCELED) {
            otpInputAdapter.deleteAllPin();
        }

        if (resultCode == Activity.RESULT_CANCELED && data != null) {
            showSnackbarErrorMessageRevamp(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR, this, false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}