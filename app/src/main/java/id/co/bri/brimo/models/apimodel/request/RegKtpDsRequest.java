package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class RegKtpDsRequest {

    @SerializedName("identity")
    @Expose
    private String identity;
    @SerializedName("born_date")
    @Expose
    private String bornDate;
    @SerializedName("phone_number")
    @Expose
    private String phoneNumber;
    @SerializedName("email")
    @Expose
    private String email;
    @SerializedName("product_id")
    @Expose
    private String productId;
    @SerializedName("branch_code")
    @Expose
    private String branchCode;
    @SerializedName("postcode")
    @Expose
    private String postcode;

    public RegKtpDsRequest(String identity, String bornDate, String phoneNumber,
                           String email, String productId, String branchCode, String postcode) {
        this.identity = identity;
        this.bornDate = bornDate;
        this.phoneNumber = phoneNumber;
        this.email = email;
        this.productId = productId;
        this.branchCode = branchCode;
        this.postcode = postcode;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }
}
