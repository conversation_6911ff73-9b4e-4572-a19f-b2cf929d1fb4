package id.co.bri.brimo.models.apimodel.response.newskinonboarding

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class CheckStatusKycRes(
    @Expose @SerializedName("status") var status: String,
    @Expose @SerializedName("expiry_in_second") var expiredInSecond: Int
) : Parcelable