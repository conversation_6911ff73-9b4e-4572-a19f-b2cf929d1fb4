package id.co.bri.brimo.adapters;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Build;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.databinding.ItemHeaderTransaksiVoucherStreamingBinding;
import id.co.bri.brimo.models.apimodel.response.DataView;

import java.util.List;


import static android.content.Context.CLIPBOARD_SERVICE;

public class HeaderTransaksiVoucherStreamingAdapter extends RecyclerView.Adapter<HeaderTransaksiVoucherStreamingAdapter.ViewHolder> {

    List<DataView> headerTransactionVoucherStreaming;
    Context context;

    private ClipboardManager myClipboard;
    private ClipData myClip;
    private String mStreamingId;

    private voucherListener adapterVoucherListener = null;

    public void setAdapterClickCopyListener(HeaderTransaksiVoucherStreamingAdapter.voucherListener adapterVoucherListener) {
        this.adapterVoucherListener = adapterVoucherListener;
    }

    public HeaderTransaksiVoucherStreamingAdapter(List<DataView> transactionDataView, Context context, String streaminId) {
        this.headerTransactionVoucherStreaming = transactionDataView;
        this.context = context;
        myClipboard = (ClipboardManager) context.getSystemService(CLIPBOARD_SERVICE);
        this.mStreamingId = streaminId;
    }

    @NonNull
    @Override
    public HeaderTransaksiVoucherStreamingAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemHeaderTransaksiVoucherStreamingBinding.inflate(LayoutInflater.from(context), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull HeaderTransaksiVoucherStreamingAdapter.ViewHolder holder, int position) {
        DataView currentDataView = headerTransactionVoucherStreaming.get(position);
        holder.binding.rlCard.setVisibility(View.VISIBLE);
        holder.binding.tvCard.setText(currentDataView.getValue());

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            holder.binding.tvCard.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
        } else {
            holder.binding.tvCard.setGravity(Gravity.CENTER_HORIZONTAL);
        }

        holder.binding.btnCopy.setOnClickListener(view1 -> {
            myClip = ClipData.newPlainText("Data", currentDataView.getValue());
            myClipboard.setPrimaryClip(myClip);
            if (adapterVoucherListener != null) {
                adapterVoucherListener.onClickCopyCaraRedeem(currentDataView.getName(), position);
            }
        });

        holder.binding.tvCaraRedeem.setOnClickListener(v -> {
            adapterVoucherListener.onClickCaraRedeem(mStreamingId);
        });
    }

    @Override
    public int getItemCount() {
        return headerTransactionVoucherStreaming.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemHeaderTransaksiVoucherStreamingBinding binding;

        public ViewHolder(@NonNull ItemHeaderTransaksiVoucherStreamingBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }


    public interface voucherListener {
        void onClickCopyCaraRedeem(String message, int position);

        void onClickCaraRedeem(String streamingId);
    }
}
