package id.co.bri.brimo.contract.IView.remittence;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.JalurResponse;
import id.co.bri.brimo.models.apimodel.response.SearchBankResponse;

public interface ISearchBankView extends IMvpView {

    void onSuccessGetJalur(SearchBankResponse searchBankResponse);

    void onExceptionNF();

    void showSkeleton();

    void hideSkeleton();
}
