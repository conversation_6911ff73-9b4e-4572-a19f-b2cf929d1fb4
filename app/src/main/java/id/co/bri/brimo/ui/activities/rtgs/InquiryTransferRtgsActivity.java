package id.co.bri.brimo.ui.activities.rtgs;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.annotation.Nullable;

import java.math.BigInteger;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.rtgs.IInquiryTransferRtgsPresenter;
import id.co.bri.brimo.contract.IView.rtgs.IInquiryTransferRtgsView;
import id.co.bri.brimo.databinding.ActivityInquiryTransferRtgsBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.Amount;
import id.co.bri.brimo.models.BillingDetailOpen;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRtgsResponse;
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

public class InquiryTransferRtgsActivity extends BaseActivity implements
        AmountFormatWatcher.onAmountChange,
        SumberDanaFragment.SelectSumberDanaInterface,
        View.OnClickListener,
        IInquiryTransferRtgsView {

    private ActivityInquiryTransferRtgsBinding binding;

    private static final String TAG = "InquiryTransferRtgsActivity";


    public static InquiryTransferRtgsResponse inquiryTransferRtgsResponse;

    private List<BillingDetailOpen> mbrivaOpenResponse;
    private List<BillingDetailOpen> mbrivaOpenResponseAdress;
    BillingDetailOpen openModel;
    BillingDetailOpen openModelAddress;

    protected List<Integer> mListFailed;
    protected List<AccountModel> mListAccountModel;
    protected AccountModel model;
    protected Double saldo = 0.0;
    protected int counter = 0;
    protected Long minTrx = Long.valueOf(0);
    protected String minTrxString;
    protected boolean isLoading, isCheckedSave = false;
    protected String saldoString = "";
    protected String defaultAkun;
    protected Amount amountPay = new Amount();

    private static String mUrlKonfirmasi;
    private static String mUrlPayment;
    private static ParameterModel mParameterModel;

    protected static boolean mIsFromTopUpOnline;
    protected static String errorMessage = null;
    private static InquiryTransferRtgsResponse mInquiryResponse;
    protected String mUrlPending = "";
    protected String mTitle;
    protected ParameterKonfirmasiModel mParameterKonfirmasiModel;

    @Inject
    IInquiryTransferRtgsPresenter<IInquiryTransferRtgsView> inquiryTransferRtgsPresenter;

    public static void launchIntent(Activity caller, InquiryTransferRtgsResponse inquiryTransferRtgsResponse, ParameterModel parameterModel, boolean isFromFastMenuL) {
        Intent intent = new Intent(caller, InquiryTransferRtgsActivity.class);
        mInquiryResponse = inquiryTransferRtgsResponse;
        mParameterModel = parameterModel;
        isFromFastMenu = isFromFastMenuL;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquiryTransferRtgsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbInquiryToolbar.toolbar, GeneralHelper.getString(R.string.transfer_title_bar));

        injectDependency();

        setupView();
        setupTextWatcher();

        binding.itemLayoutBackground.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);

        mParameterKonfirmasiModel = setParameterKonfirmasi();

        //set listener untuk field inputan nama
        if (binding.etSavedName != null) {
            binding.etSavedName.addTextChangedListener(activityTextListener);
        }


    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (isFromFastMenu) {
            mUrlKonfirmasi = GeneralHelper.getString(R.string.url_confirm_transfer_alias_fm_rtgs_v2);
            mUrlPayment = GeneralHelper.getString(R.string.url_payment_transfer_alias_fm_rtgs_v2);
        } else {
            mUrlKonfirmasi = GeneralHelper.getString(R.string.url_transfer_confirmation_alias_rtgs_v2);
            mUrlPayment = GeneralHelper.getString(R.string.url_transfer_payment_alias_rtgs_v2);
        }
        if (inquiryTransferRtgsPresenter != null) {
            inquiryTransferRtgsPresenter.setView(this);
            inquiryTransferRtgsPresenter.setUrlConfirmation(mUrlKonfirmasi);
            inquiryTransferRtgsPresenter.start();
        }
    }

    @Override
    protected void afterText(Editable editable) {
        //balikan listener field inputan nama
        try {
            String savedText = binding.etSavedName.getText().toString();
            if (savedText.equalsIgnoreCase("") || !ValidationHelper.validateAmountString(amountPay.getText())) {
                disableButtonSubmit(true);
                setupInputError(binding.edNominal);
            } else if (!savedText.equalsIgnoreCase("") && saldo <= 0) {
                disableButtonSubmit(true);
                setupInputError(binding.edNominal);
            } else {
                disableButtonSubmit(false);
                setupInputError(binding.edNominal);
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "afterText: ", e);
            }
        }
    }

    /**
     * Callback dari get saldo default dari BRImoPref
     *
     * @param saldoDefault    saldo dalam double
     * @param saldoStringPref saldo dalam bentuk string
     * @param defaultAcc      rekening default
     */
    @Override
    public void setDefaultSaldo(double saldoDefault, String saldoStringPref, String defaultAcc) {
        // load default saldo dari preference
        saldoString = saldoStringPref;
        saldo = saldoDefault;
        defaultAkun = defaultAcc;

        if (binding.edNominal != null) {
            setupInputError(binding.edNominal);
        }

        //set layout
        setupAccount(saldoDefault);
        setupSaveName();
        setupView();
    }

    @Override
    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan(mParameterModel.getStringLabelTujuan());
        parameterKonfirmasiModel.setStringButtonSubmit(mParameterModel.getStringButtonSubmit());
        parameterKonfirmasiModel.setDefaultIcon(mParameterModel.getDefaultIcon());

        return parameterKonfirmasiModel;

    }


    private void setupView() {
        if (mInquiryResponse.getBillingDetailOpen() == null)
            return;

        try {
            openModel = new BillingDetailOpen();
            mbrivaOpenResponse = mInquiryResponse.getBillingDetailOpen();
        } catch (Exception e) {
//           e.printStackTrace();
        }

        try {
            openModelAddress = new BillingDetailOpen();
            mbrivaOpenResponseAdress = mInquiryResponse.getBillingAddresses();
        } catch (Exception e) {
//           e.printStackTrace();
        }

        for (BillingDetailOpen billingDetailOpen : mbrivaOpenResponse) {
            openModel = billingDetailOpen;
        }

        for (BillingDetailOpen billingDetailOpenAddress : mbrivaOpenResponseAdress) {
            openModelAddress = billingDetailOpenAddress;
        }

        //View
        binding.tvNamaBriva.setText(openModel.getTitle());
        binding.tvNomorBriva.setText(openModel.getSubtitle());

        if (openModelAddress.getSubtitle() != null) {
            binding.tvAlamatPenerima.setText(openModelAddress.getSubtitle());
        }

        //Set Image Circle
        if (openModel.getListType().equals("image")) {
            binding.llLogoOpen.setVisibility(View.VISIBLE);
            binding.rlInisialOpen.setVisibility(View.GONE);
        } else {
            binding.llLogoOpen.setVisibility(View.GONE);
            binding.rlInisialOpen.setVisibility(View.VISIBLE);
        }

        //Set Initial
        String title = openModel.getTitle();
        String initial = GeneralHelper.formatInitialName(title);
        binding.tvInisialOpen.setText(initial);

        //disable button submit
        disableButtonSubmit(true);

        //set minimum
        if (mInquiryResponse.getMinimumTransaction() != null && mInquiryResponse.getMinimumTransactionString() != null) {
            minTrx = mInquiryResponse.getMinimumTransaction();
            minTrxString = mInquiryResponse.getMinimumTransactionString();
        }

        if (mParameterModel != null) {
            //load icon transaction
            GeneralHelper.loadIconTransaction(
                    this,
                    openModel.getIconPath(),
                    openModel.getIconName().split("\\.")[0],
                    binding.ivIconOpen,
                    mParameterModel.getDefaultIcon());

            //Set lbl
            binding.lblTujuan.setText(mParameterModel.getStringLabelTujuan());
            binding.lblNominal.setText(mParameterModel.getStringLabelNominal());
            binding.btnSubmit.setText(mParameterModel.getStringButtonSubmit());

            binding.tvError.setText(String.format("Minimal %s %s", "Transfer", minTrxString));
            binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
            binding.layoutError.setVisibility(View.VISIBLE);

        }

        binding.llEditText.setOnClickListener(view -> {
            binding.edNominal.requestFocus();
            binding.edNominal.setFocusableInTouchMode(true);
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.showSoftInput(binding.edNominal, InputMethodManager.SHOW_FORCED);
        });

    }

    private void setupTextWatcher() {
        binding.edNominal.addTextChangedListener(new AmountFormatWatcher(binding.edNominal, this, false));
    }

    /**
     * Dipanggil ketika callback dari
     * 1. presenter.start
     * 2. presenter.getSaldoDefault
     * 3. this.setDefaultSaldo()
     * setup account digunakan utk mengisi list account pada SumberDanaFragment
     * method ini mempengaruhi method onSelectSumberDana()
     *
     * @param saldoDefault
     */
    protected void setupAccount(double saldoDefault) {
        if (mInquiryResponse == null)
            finish();

        //List Account
        model = new AccountModel();
        if (mInquiryResponse.getAccountModel().size() > 0)
            mListAccountModel = mInquiryResponse.getAccountModel();

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                model = accountModel;
                break;
            } else {
                model = mListAccountModel.get(0);
            }
        }

        //jika get minimum tidak null
        if (model.getMinimumBalance() != null) {
            saldo = saldoDefault - model.getMinimumBalance();
        } else {
            saldo = saldoDefault;
        }

        if (isFromFastMenu) {
            binding.tvSaldo.setVisibility(View.GONE);
        } else {
            binding.tvSaldo.setVisibility(View.VISIBLE);

            if (model.getAcoount() != null) {
                if (model.getAcoount().equals(defaultAkun)) {
                    //tvSaldo.setText(String.format("%s%s", model.getCurrency(), saldoString));
                    binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), saldoString));
                } else {
                    binding.tvSaldo.setText("-");
                }
            }

        }

        //jika string rekening tidak kosong
        if (model.getAcoountString() != null) {
            binding.tvNorek.setText(model.getAcoountString());
        } else {
            binding.tvNorek.setText("-");
        }

        //jika nama kosong
        if (model.getName() != null) {
            binding.tvInisial.setText(GeneralHelper.formatInitialName(model.getName()));
        } else {
            binding.tvInisial.setText("I");
        }

    }


    protected void setupSaveName() {
        if (mInquiryResponse == null)
            finish();
        if (mInquiryResponse.getSaved() != null) {
            if (mInquiryResponse.getSaved().equals("")) {
                binding.saveContent.setVisibility(View.GONE);
                binding.checkboxSave.setOnClickListener(view -> {
                    if (binding.checkboxSave.isChecked()) {
                        onAnimatorShow(binding.saveContent, true, Constant.REQUEST_INQUIRY);
                        //
                        isCheckedSave = true;
                        setupInputError(binding.edNominal);
                    } else {
                        onAnimatorFade(binding.saveContent, false, Constant.REQUEST_INQUIRY);
                        // disableButtonSubmit(false);
                        isCheckedSave = false;
                        setupInputError(binding.edNominal);
                    }
                });

                binding.tvSave.setOnClickListener(view -> {
                    if (!isCheckedSave) {
                        binding.checkboxSave.setChecked(true);
                        onAnimatorShow(binding.saveContent, true, Constant.REQUEST_INQUIRY);
                        //
                        isCheckedSave = true;
                        setupInputError(binding.edNominal);
                    } else {
                        binding.checkboxSave.setChecked(false);
                        onAnimatorFade(binding.saveContent, false, Constant.REQUEST_INQUIRY);
                        // disableButtonSubmit(false);
                        isCheckedSave = false;
                        setupInputError(binding.edNominal);
                    }
                });
            } else {
                binding.viewSaved.setVisibility(View.GONE);
            }
        } else {
            binding.viewSaved.setVisibility(View.GONE);
        }
    }


    @Override
    protected void onAnimatorFadeEnd(String tagIdFade) {
        super.onAnimatorFadeEnd(tagIdFade);

        if (tagIdFade.equals(Constant.REQUEST_INQUIRY)) {
            binding.saveContent.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onAnimatorShowEnd(String tagId) {
        super.onAnimatorShowEnd(tagId);
        if (tagId.equals(Constant.REQUEST_INQUIRY)) {
            binding.saveContent.setVisibility(View.VISIBLE);
        }
    }

    /*
     * Method ini digunakan untuk validasi saldo cukup.
     *
     */
    protected void setupInputError(EditText edNominal) {

        if (edNominal.getText().toString().isEmpty()) {
            disableButtonSubmit(true);
            onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
            showAmountMinimal();
            return;
        }

        amountPay.setText(GeneralHelper.clearingAmount(edNominal.getText().toString()));

        //jika bisa menyimpan atau ceklist save true
        if (isCheckedSave) {
            if (ValidationHelper.validateAmountString(amountPay.getText()) && !binding.etSavedName.getText().toString().equals("")) {
                if (BigInteger.valueOf(minTrx).compareTo(BigInteger.valueOf(Long.valueOf(amountPay.getText()))) <= 0) {
                    if (BigInteger.valueOf(Long.valueOf(amountPay.getText())).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0 || isFromFastMenu) {
                        onAnimator(binding.layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(false);
                    } else {
                        onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                        showAmountInsuficient();
                        disableButtonSubmit(true);
                    }
                } else {
                    onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                    showAmountMinimal();
                    disableButtonSubmit(true);
                }
            } else if (!ValidationHelper.validateAmountString(amountPay.getText())) {
                onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                showAmountMinimal();
                disableButtonSubmit(true);
            } else if (binding.etSavedName.getText().toString().equals("") && ValidationHelper.validateAmountString(amountPay.getText())) {
                disableButtonSubmit(true);
            } else {
                onAnimator(binding.layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                disableButtonSubmit(false);
            }


        } else {
            if (ValidationHelper.validateAmountString(amountPay.getText())) {
                if (BigInteger.valueOf(minTrx).compareTo(BigInteger.valueOf(Long.valueOf(amountPay.getText()))) <= 0) {
                    if (BigInteger.valueOf(Long.valueOf(amountPay.getText())).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0 || isFromFastMenu) {
                        onAnimator(binding.layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(false);
                    } else {
                        onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                        showAmountInsuficient();
                        disableButtonSubmit(true);
                    }
                } else {
                    onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                    showAmountMinimal();
                    disableButtonSubmit(true);
                }
            } else if (!ValidationHelper.validateAmountString(amountPay.getText())) {
                onAnimator(binding.layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                showAmountMinimal();
                disableButtonSubmit(true);
            } else {
                onAnimator(binding.layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                disableButtonSubmit(false);
            }
        }
    }

    /**
     * Show parameter minimal di field
     */
    protected void showAmountMinimal() {
        if (mInquiryResponse.getMinimumAmountString() != null) {
            binding.tvError.setText(String.format("Minimal %s %s", "Transfer", mInquiryResponse.getMinimumTransactionString()));
            binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        }
    }

    /**
     * Show parameter saldo tidak cukup
     */
    protected void showAmountInsuficient() {
        binding.tvError.setText("Saldo Anda tidak cukup");
        binding.tvError.setTextColor(getResources().getColor(R.color.red));
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    if (isFromFastMenu) {
                        ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                    } else {
                        SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                    }

                }
                break;
            case R.id.btnSubmit:
                onSubmit();
                break;
        }
    }

    @Override
    public void onException93(String message) {
        isLoading = false;
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public int getAmount() {
        return amountPay.getValue();
    }

    @Override
    public void onSubmit() {
        inquiryTransferRtgsPresenter.getDataConfirmation(mInquiryResponse.getReferenceNumber(), model.getAcoount(), amountPay.getText(), binding.etSavedName.getText().toString(), isFromFastMenu);
    }

    @Override
    public void onException(String message) {
        isLoading = false;
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showDialogGagalBack(this, message);
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
        }
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;

        if (model.getSaldoReponse() != null) {
            binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getSaldoReponse().getBalanceString()));
            saldo = model.getSaldoReponse().getBalance() - model.getMinimumBalance();
        } else {
            binding.tvSaldo.setText(String.format("%s%s", model.getCurrency(), "-"));
            saldo = 0.0;
        }
        binding.tvNorek.setText(model.getAcoountString());
        binding.tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));

        setupInputError(binding.edNominal);
    }


    /*
     * Menambahkan List Rekening
     */
    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    @Override
    public void onSuccessGetConfirmation(GeneralConfirmationResponse brivaConfirmationResponse) {
        isLoading = false;
        KonfirmasiGeneralActivity.launchIntent(this, brivaConfirmationResponse, mUrlPayment, GeneralHelper.getString(R.string.transfer_title_bar), mParameterKonfirmasiModel, isFromFastMenu, true, false);
    }

    protected void disableButtonSubmit(boolean disable) {
        if (disable) {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        } else {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }
    }


    @Override
    public void onAmountChange(String amount) {
        amountPay.setText(amount);
        setupInputError(binding.edNominal);
    }

    @Override
    public void setAmountListener() {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}