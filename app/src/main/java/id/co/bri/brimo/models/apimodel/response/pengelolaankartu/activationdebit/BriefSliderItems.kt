package id.co.bri.brimo.models.apimodel.response.pengelolaankartu.activationdebit


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class BriefSliderItems(
    @SerializedName("items")
    val sliderItems: List<SliderItems> = listOf(),
    @SerializedName("title")
    val title: String = "" // Panduan Pembuatan
): Parcelable