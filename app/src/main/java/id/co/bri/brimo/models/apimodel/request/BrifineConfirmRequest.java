package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class BrifineConfirmRequest {
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("product_code")
    @Expose
    private String productCode;
    @SerializedName("income_code")
    @Expose
    private String incomeCode;
    @SerializedName("job_code")
    @Expose
    private String jobCode;
    @SerializedName("subscription_amount")
    @Expose
    private String subscriptionAmount;
    @SerializedName("first_date")
    @Expose
    private String firstDate;
    @SerializedName("is_simpedes_bisa")
    @Expose
    private boolean isSimpedesBisa;

    public BrifineConfirmRequest(String accountNumber, String productCode, String incomeCode,
                                 String jobCode, String subscriptionAmount,
                                 String firstDate, boolean isSimpedesBisa) {
        this.accountNumber = accountNumber;
        this.productCode = productCode;
        this.incomeCode = incomeCode;
        this.jobCode = jobCode;
        this.subscriptionAmount = subscriptionAmount;
        this.firstDate = firstDate;
        this.isSimpedesBisa = isSimpedesBisa;
    }

    public boolean isSimpedesBisa() {
        return isSimpedesBisa;
    }

    public void setSimpedesBisa(boolean simpedesBisa) {
        isSimpedesBisa = simpedesBisa;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getIncomeCode() {
        return incomeCode;
    }

    public void setIncomeCode(String incomeCode) {
        this.incomeCode = incomeCode;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public String getSubscriptionAmount() {
        return subscriptionAmount;
    }

    public void setSubscriptionAmount(String subscriptionAmount) {
        this.subscriptionAmount = subscriptionAmount;
    }

    public String getFirstDate() {
        return firstDate;
    }

    public void setFirstDate(String firstDate) {
        this.firstDate = firstDate;
    }
}
