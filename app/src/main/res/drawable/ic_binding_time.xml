<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="28dp"
    android:height="24dp"
    android:viewportWidth="28"
    android:viewportHeight="24">
  <path
      android:pathData="M17.141,0.9C12.803,0.9 9.07,3.507 7.331,7.266H4.511C3.901,7.266 3.406,7.761 3.406,8.371C3.406,8.981 3.901,9.476 4.511,9.476H11.597V10.367H2.808C2.198,10.367 1.703,10.862 1.703,11.472C1.703,12.082 2.198,12.577 2.808,12.577H5.905V12.577H11.597V13.469H5.905V13.468H1.105C0.495,13.468 0,13.963 0,14.573C0,15.183 0.495,15.678 1.105,15.678H6.906C8.392,19.998 12.407,23.1 17.141,23.1C23.139,23.1 28,18.13 28,12C28,5.87 23.139,0.9 17.141,0.9Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.911"
          android:startY="1.88"
          android:endX="14.297"
          android:endY="11.109"
          android:type="linear">
        <item android:offset="0" android:color="#FF107DD5"/>
        <item android:offset="1" android:color="#FF1D66AA"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.331,7.266H4.511C3.901,7.266 3.406,7.761 3.406,8.371C3.406,8.981 3.901,9.476 4.511,9.476H11.597V10.367H2.808C2.198,10.367 1.703,10.862 1.703,11.472C1.703,12.082 2.198,12.577 2.808,12.577H5.905V12.577H11.597V13.469H5.905V13.468H1.105C0.495,13.468 0,13.963 0,14.573C0,15.181 0.491,15.674 1.098,15.678H13.12V7.265H7.331C7.331,7.266 7.331,7.266 7.331,7.266Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="2.692"
          android:startY="12.207"
          android:endX="11.849"
          android:endY="11.102"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M17.14,23.1C23.137,23.1 27.999,18.13 27.999,12C27.999,5.87 23.137,0.9 17.14,0.9C11.143,0.9 6.281,5.87 6.281,12C6.281,18.13 11.143,23.1 17.14,23.1Z"/>
    <path
        android:pathData="M23.303,29.262C29.3,29.262 34.162,24.292 34.162,18.162C34.162,12.032 29.3,7.062 23.303,7.062C17.306,7.062 12.444,12.032 12.444,18.162C12.444,24.292 17.306,29.262 23.303,29.262Z"
        android:fillColor="#0A5C97"/>
  </group>
  <path
      android:pathData="M17.141,20.817C21.904,20.817 25.766,16.869 25.766,12C25.766,7.131 21.904,3.183 17.141,3.183C12.377,3.183 8.516,7.131 8.516,12C8.516,16.869 12.377,20.817 17.141,20.817Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5.653"
          android:startY="2.689"
          android:endX="15.98"
          android:endY="11.059"
          android:type="linear">
        <item android:offset="0" android:color="#FFB4DEFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.354,16.931C21.178,16.931 21.001,16.867 20.862,16.738L16.548,12.758C16.399,12.62 16.315,12.428 16.315,12.225V7.19C16.315,6.79 16.639,6.465 17.039,6.465C17.44,6.465 17.764,6.79 17.764,7.19V11.908L21.845,15.673C22.14,15.944 22.158,16.402 21.886,16.697C21.743,16.853 21.549,16.931 21.354,16.931Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M16.394,12.375C16.417,12.54 16.483,12.695 16.607,12.809L19.861,15.812C20.052,15.237 20.122,14.639 20.117,14.076L17.853,11.988L16.394,12.375Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M21.368,16.824C21.196,16.824 21.022,16.762 20.886,16.636L16.66,12.736C16.514,12.602 16.431,12.413 16.431,12.215V7.282C16.431,6.889 16.748,6.571 17.141,6.571C17.534,6.571 17.851,6.889 17.851,7.282V11.904L21.849,15.593C22.138,15.858 22.156,16.307 21.889,16.596C21.749,16.748 21.559,16.824 21.368,16.824Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.504"
          android:startY="6.866"
          android:endX="19.169"
          android:endY="13.334"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.14,5.036C17.042,5.036 16.963,4.957 16.963,4.858V3.587C16.963,3.489 17.042,3.41 17.14,3.41C17.239,3.41 17.318,3.489 17.318,3.587V4.858C17.318,4.957 17.239,5.036 17.14,5.036Z"
      android:fillColor="#F8B167"/>
  <path
      android:pathData="M17.14,20.589C17.042,20.589 16.963,20.51 16.963,20.412V19.142C16.963,19.044 17.042,18.965 17.14,18.965C17.239,18.965 17.318,19.044 17.318,19.142V20.412C17.318,20.51 17.239,20.589 17.14,20.589Z"
      android:fillColor="#F8B167"/>
  <path
      android:pathData="M10.225,12.177H8.956C8.857,12.177 8.778,12.098 8.778,12C8.778,11.901 8.857,11.822 8.956,11.822H10.225C10.324,11.822 10.403,11.901 10.403,12C10.403,12.098 10.324,12.177 10.225,12.177Z"
      android:fillColor="#F8B167"/>
  <path
      android:pathData="M25.373,12.177H24.103C24.005,12.177 23.926,12.098 23.926,12C23.926,11.901 24.005,11.822 24.103,11.822H25.373C25.471,11.822 25.55,11.901 25.55,12C25.55,12.098 25.471,12.177 25.373,12.177Z"
      android:fillColor="#F8B167"/>
  <path
      android:pathData="M16.451,12.311C16.474,12.472 16.539,12.625 16.66,12.736L19.849,15.678C20.036,15.116 20.104,14.53 20.099,13.978L17.881,11.932L16.451,12.311Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.451"
          android:startY="13.805"
          android:endX="20.1"
          android:endY="13.805"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.142,12.845C17.608,12.845 17.986,12.467 17.986,12C17.986,11.534 17.608,11.155 17.142,11.155C16.675,11.155 16.297,11.534 16.297,12C16.297,12.467 16.675,12.845 17.142,12.845Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M17.142,12.804C17.586,12.804 17.946,12.444 17.946,12C17.946,11.556 17.586,11.196 17.142,11.196C16.698,11.196 16.338,11.556 16.338,12C16.338,12.444 16.698,12.804 17.142,12.804Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.892"
          android:startY="11.719"
          android:endX="17.506"
          android:endY="12.409"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.942,12.066C17.86,12.024 17.767,12 17.668,12C17.33,12 17.056,12.274 17.056,12.613C17.056,12.679 17.066,12.742 17.086,12.801C17.104,12.803 17.123,12.804 17.142,12.804C17.564,12.804 17.909,12.479 17.942,12.066Z"
      android:fillColor="#0A5C97"/>
  <path
      android:pathData="M16.804,12.492C16.804,12.581 16.818,12.666 16.844,12.747C16.937,12.783 17.037,12.804 17.142,12.804C17.586,12.804 17.946,12.444 17.946,12C17.946,11.899 17.926,11.802 17.892,11.712C17.809,11.684 17.72,11.668 17.627,11.668C17.173,11.668 16.804,12.037 16.804,12.492Z"
      android:fillColor="#F06F21"/>
  <path
      android:pathData="M17.141,12.702C17.529,12.702 17.843,12.388 17.843,12C17.843,11.613 17.529,11.298 17.141,11.298C16.754,11.298 16.44,11.613 16.44,12C16.44,12.388 16.754,12.702 17.141,12.702Z"
      android:fillColor="#F8B167"/>
  <path
      android:pathData="M17.766,11.68C17.721,11.672 17.675,11.668 17.627,11.668C17.173,11.668 16.804,12.037 16.804,12.492C16.804,12.535 16.807,12.578 16.814,12.62C16.912,12.672 17.023,12.702 17.142,12.702C17.53,12.702 17.844,12.388 17.844,12C17.844,11.885 17.816,11.776 17.766,11.68Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.834"
          android:startY="11.614"
          android:endX="17.673"
          android:endY="12.601"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.851,8.412V7.282C17.851,6.889 17.534,6.571 17.141,6.571C16.748,6.571 16.431,6.889 16.431,7.282V7.287C16.893,7.504 17.393,7.941 17.851,8.412Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="17.645"
          android:startY="5.319"
          android:endX="16.852"
          android:endY="9.241"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8B167"/>
        <item android:offset="1" android:color="#FFF06F21"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
