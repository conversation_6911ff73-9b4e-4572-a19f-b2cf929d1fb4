<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x1">

            <TextView
                android:id="@+id/tv_bus_name"
                style="@style/BodyMediumText.DemiBold.Black"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/ll_price_right" />

            <TextView
                android:id="@+id/tv_bus_class"
                style="@style/BodySmallText.Medium.Grey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_bus_name"
                android:layout_toStartOf="@id/ll_price_right" />

            <LinearLayout
                android:id="@+id/ll_price_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_bus_price_old"
                    style="@style/Headline6Text.Bold.BluePrimary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:alpha="0.5"
                    android:gravity="end"
                    android:text="@string/line"
                    android:textColor="@color/notif_color"
                    android:textSize="11dp"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_bus_price"
                        style="@style/Headline6Text.Bold.BluePrimary"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="@string/line" />

                    <TextView
                        android:id="@+id/tv_bus_seat"
                        style="@style/BodySmallText.Medium.Black"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true"
                        android:gravity="center"
                        android:text="@string/per_chair" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_marginBottom="@dimen/space_x1"
            android:background="@color/accent3Color" />
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x10_half"
                android:layout_marginVertical="@dimen/space_x2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="7dp"
                    android:layerType="software"
                    android:src="@drawable/dotted" />
                <ImageView
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:src="@drawable/circle_blue_line"/>
                <ImageView
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:src="@drawable/circle_blue"
                    android:layout_alignParentEnd="true"/>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="5"
                android:layout_marginTop="@dimen/space_x1">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="2">

                    <TextView
                        android:id="@+id/tv_jam_berangkat"
                        style="@style/Headline6Text.Bold.BluePrimary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:id="@+id/tv_detail_berangkat"
                        style="@style/BodySmallText.Medium.Accent1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_bus_name"
                        android:layout_marginTop="@dimen/space_x1"
                        android:maxLines="3" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/space_half"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tv_durasi"
                        style="@style/CaptionText.Bold.Black"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x1"
                        android:background="@color/white"
                        android:gravity="center"
                        android:textAlignment="center" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="2">

                    <TextView
                        android:id="@+id/tv_jam_tiba"
                        style="@style/Headline6Text.Bold.BluePrimary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAlignment="textEnd" />

                    <TextView
                        android:id="@+id/tv_detail_tiba"
                        style="@style/BodySmallText.Medium.Accent1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_bus_name"
                        android:layout_marginTop="@dimen/space_x1"
                        android:maxLines="3"
                        android:textAlignment="textEnd" />
                </LinearLayout>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="@dimen/space_half"
        android:layout_marginTop="@dimen/space_x2"
        android:background="@color/color_bg_bottomsheet" />
</LinearLayout>