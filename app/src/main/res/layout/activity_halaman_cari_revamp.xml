<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/rl_container"
    android:fitsSystemWindows="true"
    android:background="@color/neutral_light10"
    tools:context=".ui.activities.halamancarirevamp.HalamanCariRevampActivity">

    <RelativeLayout
        android:id="@+id/layout_searchview"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tb_revamp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x11_3quarter"
            android:textAlignment="center"
            android:background="@color/primary_blue80">

                <TextView
                    android:id="@+id/textTitle"
                    style="@style/SubTitleText.Bold.White"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|top"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:textAlignment="center" />
        </androidx.appcompat.widget.Toolbar>

        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchview_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tb_revamp"
            android:layout_marginTop="@dimen/space_minx3"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_half"
            android:background="@drawable/bg_white_border_blue"
            android:inputType="text"
            app:iconifiedByDefault="false"
            app:queryBackground="@color/transparent"
            app:queryHint="@string/cari_fitur_atau_kata_kunci"
            app:searchIcon="@drawable/ic_search_new"
            app:closeIcon="@drawable/ic_close_new"
            android:visibility="visible"/>
    </RelativeLayout>
    
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/neutral_light10"
        android:layout_below="@+id/layout_searchview">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/layout_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fillViewport="true"
            android:scrollbars="vertical"
            android:background="@color/white"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_coba_cari"
                    style="@style/Body3SmallText.Bold.NeutralLight80"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_marginStart="@dimen/space_x2"
                    android:layout_marginEnd="@dimen/space_x2"
                    android:layout_marginBottom="6dp"
                    android:text="@string/txt_coba_cari"
                    android:visibility="visible"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_list_fitur"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    android:visibility="gone"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_list_suggestion"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    android:visibility="visible"/>

                <View
                    android:id="@+id/view"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x1"
                    android:background="@color/neutral_light10"/>

                <LinearLayout
                    android:id="@+id/ll_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/neutral_light10"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_minx4"
                        android:layout_marginEnd="@dimen/space_minx4"
                        style="@style/Body1LargeText.Bold.PrimaryBlue80"
                        android:layout_marginTop="@dimen/space_x6"
                        android:textSize="@dimen/text_body2"
                        android:text="@string/desc_footer_search_title"
                        android:gravity="center"/>

                    <TextView
                        android:id="@+id/tv_subtitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        style="@style/Body3SmallText.Regular.NeutralDark10"
                        android:maxLines="2"
                        android:layout_marginTop="@dimen/space_x1"
                        android:layout_marginStart="@dimen/space_minx4"
                        android:layout_marginEnd="@dimen/space_minx4"
                        android:layout_marginBottom="@dimen/space_x6"
                        android:text="@string/desc_footer_search_title"
                        android:gravity="center"/>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>