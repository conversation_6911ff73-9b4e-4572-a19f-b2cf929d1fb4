<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".ui.fragments.dplkrevamp.SimulasiDetailDplkFragment">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/ll_content_combination_dplk"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.github.mikephil.charting.charts.PieChart
                        android:id="@+id/chart_combination"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/size_200dp"
                        android:layout_gravity="center"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginBottom="@dimen/space_x2" />

                    <LinearLayout
                        android:id="@+id/ll_warning_combination"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:background="@drawable/rounded_line_yellow"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/space_x1_half"
                        android:paddingVertical="@dimen/space_x2">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:src="@drawable/ic_warning_yellow" />

                        <TextView
                            style="@style/Caption1SmallText.Medium.NeutralDark20"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x1"
                            android:layout_marginTop="2dp"
                            android:text="@string/txt_warning" />
                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/iv_asset"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginStart="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x2"
                            android:layout_marginEnd="@dimen/space_x1"
                            android:src="@drawable/ic_circle_black" />

                        <TextView
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_toRightOf="@id/iv_asset"
                            android:text="@string/txt_pasar_uang" />

                        <EditText
                            android:id="@+id/ed_pasar_uang"
                            style="@style/BodyMediumText.Medium.Black"
                            android:layout_width="@dimen/space_x7"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@id/tvPasar"
                            android:background="@drawable/bg_ed"
                            android:focusable="true"
                            android:focusableInTouchMode="true"
                            android:gravity="center"
                            android:inputType="number"
                            android:maxLength="2"
                            android:padding="@dimen/space_x1"
                            android:text="50"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tvPasar"
                            style="@style/BodyMediumText.Bold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="@dimen/space_x1_half"
                            android:layout_marginRight="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:text="%" />

                    </RelativeLayout>

                    <SeekBar
                        android:id="@+id/seekBar1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:max="99"
                        android:outlineAmbientShadowColor="@color/transparent"
                        android:outlineSpotShadowColor="@color/transparent"
                        android:progressDrawable="@drawable/custom_seekbar_revamp"
                        android:thumb="@drawable/ic_button_slider"
                        android:thumbOffset="0dp" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/iv_asset2"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginStart="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x2"
                            android:layout_marginEnd="@dimen/space_x1"
                            android:src="@drawable/ic_circle_black" />

                        <TextView
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:layout_toRightOf="@+id/iv_asset2"
                            android:text="@string/txt_pendapatan_tetap" />

                        <EditText
                            android:id="@+id/ed_pendapatan"
                            style="@style/BodyMediumText.Medium.Black"
                            android:layout_width="@dimen/space_x7"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@id/tvPendapatan"
                            android:background="@drawable/bg_ed"
                            android:focusable="true"
                            android:focusableInTouchMode="true"
                            android:gravity="center"
                            android:inputType="number"
                            android:maxLength="2"
                            android:padding="@dimen/space_x1"
                            android:text="50"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tvPendapatan"
                            style="@style/BodyMediumText.Bold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="@dimen/space_x1_half"
                            android:layout_marginRight="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:text="%" />

                    </RelativeLayout>

                    <SeekBar
                        android:id="@+id/seekBar2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:max="99"
                        android:outlineAmbientShadowColor="@color/transparent"
                        android:outlineSpotShadowColor="@color/transparent"
                        android:progressDrawable="@drawable/custom_seekbar_revamp"
                        android:thumb="@drawable/ic_thumb_slider_revamp"
                        android:thumbOffset="0dp" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/iv_asset3"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginStart="@dimen/space_x2"
                            android:layout_marginTop="@dimen/space_x2"
                            android:layout_marginEnd="@dimen/space_x1"
                            android:src="@drawable/ic_circle_black" />

                        <TextView
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_toRightOf="@+id/iv_asset3"
                            android:text="@string/txt_saham" />

                        <EditText
                            android:id="@+id/ed_saham"
                            style="@style/BodyMediumText.Medium.Black"
                            android:layout_width="@dimen/space_x7"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@id/tvSaham"
                            android:background="@drawable/bg_ed"
                            android:focusable="true"
                            android:focusableInTouchMode="true"
                            android:gravity="center"
                            android:inputType="number"
                            android:maxLength="2"
                            android:padding="@dimen/space_x1"
                            android:text="50"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tvSaham"
                            style="@style/BodyMediumText.Bold.Black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="@dimen/space_x1_half"
                            android:layout_marginRight="@dimen/space_x2"
                            android:layout_marginBottom="@dimen/space_x2"
                            android:text="%" />

                    </RelativeLayout>

                    <SeekBar
                        android:id="@+id/seekBar3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:max="99"
                        android:outlineAmbientShadowColor="@color/transparent"
                        android:outlineSpotShadowColor="@color/transparent"
                        android:progressDrawable="@drawable/custom_seekbar_revamp"
                        android:thumb="@drawable/ic_button_slider"
                        android:thumbOffset="0dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_title_auto_payment"
                    style="@style/Body3SmallText.SemiBold.NeutralDark40"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_content_combination_dplk"
                    android:layout_marginStart="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x2"
                    android:text="@string/setoran_awal" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_nominal"
                    style="@style/TextInputLayoutRevamp.NoBorder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_title_auto_payment"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x1"
                    android:layout_marginBottom="@dimen/space_half"
                    android:background="@drawable/background_cardview_stroked_transparent"
                    android:elevation="@dimen/size_1dp"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/space_x1_half"
                    app:endIconMode="clear_text"
                    app:expandedHintEnabled="true"
                    app:helperTextTextColor="@color/neutral_light80"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:hintTextColor="@color/neutral_light80"
                    app:prefixText="Rp"
                    app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                    app:prefixTextColor="@color/neutral_dark40">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_nominal"
                        style="@style/Title4Text.Bold.NeutralDark40"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_x1_half"
                        android:hint="@string/zero"
                        android:inputType="number"
                        android:maxLength="15"
                        android:maxLines="1"
                        android:text="@string/zero"
                        android:textColorHint="@color/neutral_dark40"
                        app:hintTextColor="@color/neutral_dark40" />


                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:id="@+id/ll_usia"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/til_nominal"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/ll_usia_saat_ini"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/space_x2">

                        <TextView
                            style="@style/Body3SmallText.SemiBold.NeutralDark40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="@dimen/space_x2_half"
                            android:text="@string/txt_usia_saat_ini" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/space_x1"
                            android:background="@drawable/background_cardview_stroked_transparent"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/til_usia_saat_ini"
                                style="@style/TextInputLayoutRevamp.NoBorder"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:elevation="@dimen/size_1dp"
                                android:paddingVertical="@dimen/space_half"
                                app:endIconMode="clear_text"
                                app:expandedHintEnabled="true"
                                app:helperTextTextColor="@color/neutral_light80"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:hintTextColor="@color/neutral_light80"
                                app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                                app:prefixTextColor="@color/neutral_dark40">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/et_usia_saat_ini"
                                    style="@style/Caption1SmallText.Regular.NeutralDark40"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="number"
                                    android:maxLength="2"
                                    android:maxLines="1"
                                    android:textColorHint="@color/colorGreyBd"
                                    app:hintTextColor="@color/neutral_dark40" />
                            </com.google.android.material.textfield.TextInputLayout>

                            <TextView
                                style="@style/Body3SmallText.SemiBold.NeutralDark10"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_half"
                                android:layout_marginEnd="@dimen/space_x1_half"
                                android:lineHeight="@dimen/space_x2_half"
                                android:text="@string/txt_thn" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_usia_saat_ini"
                            style="@style/Body3SmallText.Medium.NeutralLight60"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_x1_half"
                            android:lineHeight="@dimen/space_x2_half"
                            android:text="@string/txt_min_tahun" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_usia_pensiun"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/space_x2">

                        <TextView
                            style="@style/Body3SmallText.SemiBold.NeutralDark40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="@dimen/space_x2_half"
                            android:text="@string/txt_usia_pensiun" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginVertical="@dimen/space_x1"
                            android:background="@drawable/background_cardview_stroked_transparent"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/til_usia_pensiun"
                                style="@style/TextInputLayoutRevamp.NoBorder"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:elevation="@dimen/size_1dp"
                                android:paddingVertical="@dimen/space_half"
                                app:endIconMode="clear_text"
                                app:expandedHintEnabled="true"
                                app:helperTextTextColor="@color/neutral_light80"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:hintTextColor="@color/neutral_light80"
                                app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                                app:prefixTextColor="@color/neutral_dark40">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/et_usia_pensiun"
                                    style="@style/Caption1SmallText.Regular.NeutralDark40"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="55"
                                    android:inputType="number"
                                    android:maxLength="2"
                                    android:maxLines="1"
                                    android:textColorHint="@color/colorGreyBd"
                                    app:hintTextColor="@color/neutral_dark40" />
                            </com.google.android.material.textfield.TextInputLayout>

                            <TextView
                                style="@style/Body3SmallText.SemiBold.NeutralDark10"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_half"
                                android:layout_marginEnd="@dimen/space_x1_half"
                                android:lineHeight="@dimen/space_x2_half"
                                android:text="@string/txt_thn" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_usia_pensiun"
                            style="@style/Body3SmallText.Medium.NeutralLight60"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_x1_half"
                            android:lineHeight="@dimen/space_x2_half"
                            android:text="@string/txt_min_tahun" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_warning"
                    style="@style/Body3SmallText.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_usia"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginBottom="@dimen/space_x1_half"
                    android:lineHeight="@dimen/space_x2_half"
                    android:textColor="@color/semanticRed80"
                    android:visibility="gone" />

                <View
                    android:id="@+id/vw_one"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x1"
                    android:layout_below="@id/tv_warning"
                    android:background="@color/neutral_light10" />

                <LinearLayout
                    android:id="@+id/ll_setoran_rutin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/vw_one"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:padding="@dimen/space_x2">

                    <TextView
                        android:id="@+id/tv_title_autopayment"
                        style="@style/Body2MediumText.Bold.NeutralDark40"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:text="@string/tb_autopayment" />

                    <LinearLayout
                        android:id="@+id/ll_check_box"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_title_autopayment"
                        android:layout_marginTop="@dimen/space_x2"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatCheckBox
                            android:id="@+id/cb_syarat"
                            android:layout_width="@dimen/space_x3"
                            android:layout_height="@dimen/space_x3"
                            android:background="@null"
                            android:button="@drawable/checkbox_rec_blue" />

                        <TextView
                            android:id="@+id/tv_syarat"
                            style="@style/Body3SmallText.Medium.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/space_half"
                            android:text="@string/txt_desc_auto_payment_simulasi_dplk" />
                    </LinearLayout>

                    <TextView
                        style="@style/Body3SmallText.SemiBold.NeutralDark40"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:lineHeight="@dimen/space_x2_half"
                        android:text="@string/txt_desc__nominal_auto_payment_simulasi_dplk" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_setoran_rutin"
                        style="@style/TextInputLayoutRevamp.NoBorder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1"
                        android:layout_marginBottom="@dimen/space_half"
                        android:background="@drawable/background_cardview_stroked_transparent"
                        android:elevation="@dimen/size_1dp"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/space_x1_half"
                        app:endIconMode="clear_text"
                        app:expandedHintEnabled="true"
                        app:helperTextTextColor="@color/neutral_light80"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false"
                        app:hintTextColor="@color/neutral_light80"
                        app:prefixText="Rp"
                        app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                        app:prefixTextColor="@color/neutral_dark40">

                        <EditText
                            android:id="@+id/et_setoran_rutin"
                            style="@style/Title4Text.Bold.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/zero"
                            android:inputType="number"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:text="@string/zero"
                            android:textColorHint="@color/neutral_dark40"
                            app:hintTextColor="@color/neutral_dark40" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        style="@style/Body3SmallText.SemiBold.NeutralDark40"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        android:text="@string/txt_persen_kenaikan_iuran" />

                    <LinearLayout
                        android:id="@+id/ll_uprise_nominal_auto_payment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/space_x1"
                        android:layout_marginTop="@dimen/space_x1"
                        android:background="@drawable/background_cardview_stroked_transparent"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_kenaikan_iuran"
                            style="@style/TextInputLayoutRevamp.NoBorder"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:elevation="@dimen/size_1dp"
                            android:paddingVertical="@dimen/space_half"
                            app:endIconMode="clear_text"
                            app:expandedHintEnabled="true"
                            app:helperTextTextColor="@color/neutral_light80"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:hintTextColor="@color/neutral_light80"
                            app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                            app:prefixTextColor="@color/neutral_dark40">

                            <EditText
                                android:id="@+id/et_uprise_nominal"
                                style="@style/Title4Text.Bold.NeutralDark40"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/zero"
                                android:inputType="number"
                                android:maxLength="15"
                                android:maxLines="1"
                                android:text="@string/zero"
                                android:textColorHint="@color/neutral_dark40"
                                app:hintTextColor="@color/neutral_dark40" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            style="@style/Body3SmallText.SemiBold.NeutralDark10"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/space_half"
                            android:layout_marginEnd="@dimen/space_x1_half"
                            android:gravity="center_vertical"
                            android:lineHeight="@dimen/space_x2_half"
                            android:text="%" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_minimal_percentage"
                        style="@style/Caption1SmallText.Medium.NeutralLight60"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lineHeight="@dimen/space_x2_half" />
                </LinearLayout>


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_diff_data"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x6"
                    android:layout_below="@id/ll_setoran_rutin"
                    android:layout_marginStart="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_marginEnd="@dimen/space_x2"
                    android:layout_marginBottom="@dimen/space_x2"
                    android:background="@drawable/rounded_grey_line"
                    android:enabled="false"
                    android:fontFamily="@font/bri_text_bold"
                    android:shadowRadius="8"
                    android:text="@string/hitung"
                    android:textAllCaps="false"
                    android:textColor="@color/neutral_light40"
                    android:textSize="16sp" />

                <LinearLayout
                    android:id="@+id/ll_hasil_simulasi"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/btn_diff_data"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <id.co.bri.brimo.ui.customviews.textview.TextViewMo
                        android:id="@+id/tv_brifine"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x1"
                        android:gravity="center_vertical"
                        app:textStyle="@style/Body2MediumText.Bold.NeutralDark40"
                        app:textTitle="@string/txt_hasil_simulasi" />

                    <TextView
                        android:id="@+id/tv_wording_nilai_investasi"
                        style="@style/Body3SmallText.SemiBold.NeutralLight80"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x2" />

                    <LinearLayout
                        android:id="@+id/ll_list_hasil_simulasi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:background="@drawable/background_cardview_neutral10_stroked"
                        android:elevation="-5dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_estimasi_investasi"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="false"
                                android:paddingHorizontal="@dimen/space_x2"
                                android:paddingTop="@dimen/space_x2" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/success10">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_total_estimasi_investasi"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="false"
                                android:paddingHorizontal="@dimen/space_x2"
                                android:paddingTop="@dimen/space_x2" />
                        </LinearLayout>
                    </LinearLayout>

                    <WebView
                        android:id="@+id/wv_information"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/space_x2" />
                </LinearLayout>
            </RelativeLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>