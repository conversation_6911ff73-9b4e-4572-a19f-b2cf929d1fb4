<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/space_x2"
    android:orientation="horizontal"
    android:weightSum="2">

    <TextView
        android:id="@+id/tv_data_field"
        style="@style/Body3SmallText.Medium.NeutralLight80"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:singleLine="false"
        android:text="@string/empty"
        tools:text="Nama Siswa" />

    <TextView
        android:id="@+id/tv_data_value"
        style="@style/Body3SmallText.SemiBold.NeutralDark40"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_weight="1"
        android:text="@string/empty"
        android:textAlignment="textEnd"
        tools:text="DWI CHANDRA" />

</LinearLayout>