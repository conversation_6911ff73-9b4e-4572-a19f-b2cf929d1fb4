<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="@dimen/space_x1">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        >

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Body3SmallText.Medium.NeutralLight80"/>

        <ImageView
            android:id="@+id/iv_info"
            android:layout_width="@dimen/size_20dp"
            android:layout_height="@dimen/size_20dp"
            android:visibility="gone"
            android:layout_marginStart="@dimen/space_half"
            android:src="@drawable/ic_info_neutral_light_80"/>

    </LinearLayout>


    <TextView
        android:id="@+id/tv_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAlignment="viewEnd"
        style="@style/Body3SmallText.SemiBold.NeutralDark40"/>

</LinearLayout>