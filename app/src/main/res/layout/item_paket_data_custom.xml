<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_denom_custom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/space_x2"
    android:layout_marginTop="@dimen/space_x2"
    android:minHeight="70dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/corner_radius_cardview">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_paket_data_custom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/space_x2">

        <TextView
            android:id="@+id/tvPaketTitle_custom"
            style="@style/Body3SmallText.SemiBold.NeutralDark40"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:maxLines="1" />

        <TextView
            android:id="@+id/tvPaketSubTitle_custom"
            style="@style/Caption1SmallText.Medium.NeutralLight80"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvPaketTitle_custom"
            android:layout_marginTop="@dimen/space_half"
            app:layout_constraintTop_toBottomOf="@id/tvPaketTitle_custom"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/ll_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tvPaketSubTitle_custom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvPaketLebih_custom"
            android:paddingVertical="@dimen/space_half"
            android:layout_marginTop="@dimen/space_x1"
            android:layout_marginEnd="@dimen/space_x1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPaketAmount_custom"
                style="@style/Body2MediumText.Bold.NeutralDark40"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|start"
                />

            <TextView
                android:id="@+id/tvPaketAmount_striped_custom"
                style="@style/Caption2MicroText.Regular.NeutralDark40"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_half"
                android:gravity="center_vertical|start"
                android:visibility="visible"
                />

        </LinearLayout>

        <TextView
            android:id="@+id/tvPaketLebih_custom"
            style="@style/Caption2MicroText.Bold.PrimaryBlue80"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_blue_border_radius4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:selectableItemBackground"
            android:gravity="center"
            android:paddingHorizontal="@dimen/size_14dp"
            android:paddingVertical="@dimen/space_x1"
            android:text="@string/paket_lihat"
            app:layout_constraintBottom_toBottomOf="@id/ll_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ll_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>