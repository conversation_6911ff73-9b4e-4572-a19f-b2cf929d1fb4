<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/size_10dp"
    android:orientation="horizontal"
    android:weightSum="2">

    <TextView
        android:id="@+id/tv_data_field"
        style="@style/BodySmallText.Medium.Black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:singleLine="false"
        android:text="@string/empty" />

    <TextView
        android:id="@+id/tv_data_value"
        style="@style/BodySmallText.DemiBold.Black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_weight="0.8"
        android:singleLine="false"
        android:text="@string/empty"
        android:textAlignment="viewEnd" />
</LinearLayout>