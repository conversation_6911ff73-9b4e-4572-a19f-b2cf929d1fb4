<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/space_x21"
    android:background="@color/transparent"
    android:padding="@dimen/space_x2">

    <TextView
        android:id="@+id/tv_info"
        style="@style/Caption2MicroText.Medium.NeutralDark10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginBottom="@dimen/space_half"
        android:text="@string/main_account" />

    <LinearLayout
        android:id="@+id/ll_account_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_info"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_account_number"
            android:layout_width="@dimen/space_x27_half"
            android:layout_height="@dimen/space_x2"
            android:layout_gravity="center_vertical"
            android:scaleType="fitXY"
            android:layout_marginTop="@dimen/space_half"
            android:src="@drawable/img_title_placeholder_light_mode" />

        <ImageView
            android:id="@+id/iv_name"
            android:layout_width="@dimen/space_x18"
            android:layout_height="@dimen/size_14dp"
            android:layout_gravity="center_vertical"
            android:scaleType="fitXY"
            android:layout_marginTop="@dimen/space_x1"
            android:src="@drawable/img_title_placeholder_light_mode" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_username"
        style="@style/Caption2MicroText.Medium.PrimaryBlue100"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_account_number"
        android:layout_alignParentStart="true"
        android:layout_marginTop="@dimen/space_half" />

    <ImageView
        android:id="@+id/iv_brimo_logo"
        android:layout_width="@dimen/space_x4"
        android:layout_height="@dimen/space_x4"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:src="@drawable/brimo_logo_warna" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_account_number"
        android:layout_alignParentStart="true"
        android:layout_marginTop="@dimen/space_x3"
        android:orientation="horizontal"
        android:weightSum="4">

        <LinearLayout
            android:id="@+id/ll_transfer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/space_half"
            android:layout_weight="1"
            android:background="@drawable/bg_menu_widget_light_mode"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/space_x1">

            <ImageView
                android:layout_width="@dimen/space_x3"
                android:layout_height="@dimen/space_x3"
                android:src="@drawable/ic_transfer_blue" />

            <TextView
                style="@style/Caption2MicroText.SemiBold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_half"
                android:text="@string/widget_transfer" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_qris"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:background="@drawable/bg_menu_widget_light_mode"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/space_half">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_qris_blue" />

            <TextView
                style="@style/Caption2MicroText.SemiBold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_half"
                android:text="@string/widget_qris" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_ewallet"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:background="@drawable/bg_menu_widget_light_mode"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/space_half">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_wallet_blue" />

            <TextView
                style="@style/Caption2MicroText.SemiBold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_half"
                android:text="@string/widget_ewallet" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_brizzi"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_menu_widget_light_mode"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/space_half">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_brizzi_blue" />

            <TextView
                style="@style/Caption2MicroText.SemiBold.PrimaryBlue80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_half"
                android:text="@string/widget_brizzi" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>