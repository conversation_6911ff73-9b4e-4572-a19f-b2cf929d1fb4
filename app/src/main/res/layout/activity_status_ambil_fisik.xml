<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.emas.StatusAmbilFisikActivity">

    <RelativeLayout
        android:id="@+id/rl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <include
            android:id="@+id/tb_status_ambil_fisik"
            layout="@layout/toolbar_revamp_no_elevation" />
    </RelativeLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_below="@id/rl_toolbar"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/margin_from_bottom_layout">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_360dp"
        android:layout_below="@id/rl_toolbar"
        android:background="@drawable/ic_bg_cetak_emas"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/rl_toolbar"
        android:layout_above="@id/layout_button"
        >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Lacak Status Ambil Emas Fisik"
                android:layout_marginStart="@dimen/space_x2"
                style="@style/Body2MediumText.Bold.NeutralBaseWhite"
                />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Gunakan fitur tracking di bawah untuk memantau status pengajuan cetak emasmu."
                android:layout_below="@id/tv_header"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginEnd="@dimen/space_x2"
                android:textColor="@color/neutral_baseWhite"
                android:textSize="@dimen/size_default_14sp"
                android:fontFamily="@font/bri_digital_text_regular"
                android:layout_marginTop="@dimen/space_x1"
                />

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_tracking"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_desc"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginEnd="@dimen/space_x2"
                app:cardCornerRadius="@dimen/space_x1"
                app:cardElevation="@dimen/space_x1"
                android:layout_marginTop="@dimen/space_x3"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/space_x2"
                    >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Status Ambil Fisik"
                        style="@style/Body3SmallText.SemiBold.NeutralDark40"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_2sdp"
                        android:background="@color/neutralLight20"
                        android:layout_marginTop="@dimen/space_x1_half"
                        />

                    <WebView
                        android:id="@+id/wv_estimasi_waktu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_tracking"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x2"
                        />

                    <WebView
                        android:id="@+id/wv_desc_info_pengambilan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_x4"
                        />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_detail_biayatrx"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_6dp"
                android:layout_below="@id/cv_tracking"
                android:padding="@dimen/space_x2"
                android:elevation="2dp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    >

                    <LinearLayout
                        android:id="@+id/ly_detail_transaksi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@color/neutral_baseWhite"
                        android:padding="@dimen/space_x2"
                        >

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Detail Transaksi"
                            style="@style/Body2MediumText.Bold.NeutralDark40"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvDetailTransaksi"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_x2"
                            />

                    </LinearLayout>
                    <View
                        android:id="@+id/line_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_x2"
                        android:layout_below="@id/cv_detail_biayatrx"
                        android:background="@color/neutralLight10"
                        />

                    <LinearLayout
                        android:id="@+id/ly_detail_biaya_cetak"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_below="@id/ly_detail_transaksi"
                        android:layout_marginTop="@dimen/size_6dp"
                        android:background="@color/neutral_baseWhite"
                        android:padding="@dimen/space_x2"
                        >

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Detail Biaya Cetak"
                            style="@style/Body2MediumText.Bold.NeutralDark40"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_biaya_cetak"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_x2"
                            />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/background_cardview_noborder"
                        android:padding="@dimen/space_x2"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginBottom="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x1">
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_total_payment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:overScrollMode="never" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>
        </RelativeLayout>
    </ScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/layout_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_alignParentBottom="true"
        android:background="@color/neutral_baseWhite"
        >

        <Button
            android:id="@+id/bt_lihat_bukti_trx"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_margin="15dp"
            android:background="@drawable/button_primary_bg"
            android:fontFamily="@font/avenir_next_bold"
            android:text="Lihat Bukti Transaksi"
            android:textAllCaps="false"
            android:textSize="@dimen/size_text_16sp" />

    </LinearLayout>

</RelativeLayout>
