<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="id.co.bri.brimo.ui.activities.FormMpnActivity">

    <include
        android:id="@+id/tb_input_kai"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_input_kai">

        <LinearLayout
            android:id="@+id/form_briva"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tb_input_briva"
            android:layout_marginTop="@dimen/space_x2"
            android:orientation="vertical">

            <TextView
                style="@style/BodyMediumText.Bold.BluePrimary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginBottom="@dimen/space_x2"
                android:text="@string/payment" />

            <LinearLayout
                android:id="@+id/ll_billing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/iv_icon_kai"
                        android:layout_width="@dimen/space_x5"
                        android:layout_height="@dimen/space_x5"
                        android:layout_alignParentTop="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_x2"
                        android:layout_marginEnd="@dimen/space_x2"
                        android:src="@drawable/ic_bayar" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_x2"
                    android:inputType="number"
                    android:orientation="vertical">

                    <TextView
                        style="@style/BodySmallText.Medium.Grey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/billing_code" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/tiKai"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false">

                        <EditText
                            android:id="@+id/et_no_kai"
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@android:color/transparent"
                            android:fontFamily="@font/avenir_next_demi"
                            android:hint="@string/fill_billing_code"
                            android:inputType="number"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:paddingTop="@dimen/space_x1"
                            android:paddingBottom="@dimen/space_x1"
                            android:textColorHint="@color/colorAccent"
                            tools:singleLine="true" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
            </LinearLayout>

            <View
                android:id="@+id/view"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginEnd="@dimen/space_x2"
                android:background="@color/accent2Color" />

        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/rlKeterangan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ll_button"
        android:layout_gravity="bottom"
        android:layout_marginStart="@dimen/space_x2_half"
        android:layout_marginEnd="@dimen/space_x2_half"
        android:layout_marginBottom="@dimen/space_x2_half"
        android:background="@drawable/rounded_line_blue"
        android:outlineProvider="bounds"
        android:padding="@dimen/space_x1_half">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="#eef2f6"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/space_half">

                <ImageView
                    android:layout_width="@dimen/space_x2_half"
                    android:layout_height="@dimen/space_x2_half"
                    android:layout_marginStart="@dimen/space_x1"
                    android:src="@drawable/alert_brizzi"
                    android:textColor="#00529c" />

                <TextView
                    style="@style/BodyMediumText.Bold.BluePrimary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_x1"
                    android:gravity="center_horizontal"
                    android:text="@string/informasi" />
            </LinearLayout>

            <WebView
                android:id="@+id/webview_informasi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_minx3"
                android:scrollbars="none"
                tools:ignore="WebViewLayout" />
        </LinearLayout>


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorButtonGrey"
        android:orientation="vertical">

        <View
            android:id="@+id/view2"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_gravity="top"
            android:background="@drawable/toolbar_dropshadow" />

        <Button
            android:id="@+id/btTambahbaru"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:alpha="0.3"
            android:background="@drawable/rounded_button_blue"
            android:enabled="false"
            android:fontFamily="@font/avenir_next_bold"
            android:text="@string/buttonLanjut"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="16dp" />
    </LinearLayout>

</RelativeLayout>