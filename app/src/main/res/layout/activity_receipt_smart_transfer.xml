<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_blue80"
    tools:context=".ui.activities.smarttransfer.ReceiptSmartTransferActivity">

    <include
        android:id="@+id/tb_smart_transfer_success"
        layout="@layout/toolbar_not_elavated_revamp" />

    <ImageView
        android:id="@+id/iv_success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tb_smart_transfer_success"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/space_x2"
        android:contentDescription="success_smart_transfer"
        android:src="@drawable/ic_regis_success" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Body2MediumText.Bold.NeutralBaseWhite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_success"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/space_x2"
        android:layout_marginTop="@dimen/space_x2"
        tools:text="Aktivasi Berhasil!" />

    <TextView
        android:id="@+id/tv_desc"
        style="@style/Body3SmallText.Medium.NeutralBaseWhite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_title"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/space_x2"
        android:layout_marginTop="@dimen/space_x2"
        android:textAlignment="center"
        tools:text="Selanjutnya, kamu bebas atur rekening milikmu yang ingin direkomendasikan sebagai tujuan transfer melalui halaman Smart Transfer." />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_atur_sekarang"
        style="@style/ButtonSecondary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/btn_home"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginBottom="@dimen/space_x1_half"
        android:paddingHorizontal="@dimen/space_x1_half"
        android:paddingVertical="@dimen/space_x1_half"
        android:text="@string/ke_pengaturan_biometric"
        tools:ignore="RelativeOverlap" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_home"
        style="@style/ButtonWhiteOutline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginBottom="@dimen/space_x2"
        android:paddingHorizontal="@dimen/space_x1_half"
        android:paddingVertical="@dimen/space_x1_half"
        android:text="@string/kembali_ke_home"
        tools:ignore="RelativeOverlap" />
</RelativeLayout>