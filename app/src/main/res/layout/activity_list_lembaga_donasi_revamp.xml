<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.donasirevamp.ListLembagaDonasiRevampActivity">

    <include
        android:id="@+id/tb_revamp"
        layout="@layout/toolbar_revamp_no_elevation" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_revamp"
        android:orientation="horizontal">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/neutral_baseWhite">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/space_x2">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/searchview"
                    style="@style/Body3SmallText.Medium.NeutralLight60"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_x2"
                    android:background="@drawable/bg_grey_border"
                    android:paddingStart="@dimen/space_minx1_half"
                    android:paddingEnd="@dimen/space_x1_half"
                    app:closeIcon="@drawable/ic_cancel_neutrallight80"
                    app:iconifiedByDefault="false"
                    app:queryBackground="@color/transparent"
                    app:queryHint="@string/donasi_revamp_cari_lembaga"
                    app:searchIcon="@drawable/ic_search_outline_16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_product_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/searchview"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layoutAnimation="@anim/layout_animation_fade_in" />

                <LinearLayout
                    android:id="@+id/ll_no_data_saved_found"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/searchview"
                    android:layout_marginTop="@dimen/space_x1_half"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        style="@style/Body2MediumText.SemiBold.NeutralDark40"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:text="@string/no_favorit_found"
                        android:textAlignment="center" />

                    <TextView
                        android:id="@+id/tv_no_data_found_desc"
                        style="@style/Body3SmallText.Medium.NeutralLight80"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_half"
                        android:text="@string/no_data_found_desc"
                        android:textAlignment="center" />
                </LinearLayout>
            </RelativeLayout>
        </ScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>