<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorButtonGrey"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context="id.co.bri.brimo.ui.activities.KonfirmasiTartunNdsActivity">

    <include
        android:id="@+id/tb_konfirmasi"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tb_konfirmasi"
        android:layout_marginBottom="@dimen/margin_from_bottom_layout"
        android:background="@color/colorButtonGrey"
        android:scrollbarAlwaysDrawVerticalTrack="true">

        <ScrollView
            android:id="@+id/scrollId"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/content"
            android:background="@color/colorButtonGrey">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorButtonGrey">

                <LinearLayout
                    android:id="@+id/lin1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="16dp"
                            android:layout_marginBottom="24dp">

                            <LinearLayout
                                android:id="@+id/layout_sumber_dana"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/lbl_sumber"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/avenir_next_bold"
                                    android:text="Sumber Dana"
                                    android:textColor="@color/colorMenuBottomNavigation"
                                    android:textSize="@dimen/size_konfirmasi_subtitle"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:id="@+id/item_layout_background"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/_8sdp"
                                    android:orientation="horizontal"
                                    android:paddingLeft="8dp"
                                    android:paddingTop="10dp"
                                    android:paddingRight="8dp"
                                    android:paddingBottom="10dp">

                                    <RelativeLayout
                                        android:id="@+id/rl_sumberdana"
                                        android:layout_width="36dp"
                                        android:layout_height="36dp"
                                        android:background="@drawable/round_button"
                                        android:backgroundTint="@color/colorPrimaryDark"
                                        android:orientation="vertical"
                                        android:visibility="visible">

                                        <ImageView
                                            android:id="@+id/iv_inisial"
                                            android:layout_width="31dp"
                                            android:layout_height="match_parent"
                                            android:layout_gravity="center" />

                                        <TextView
                                            android:id="@+id/tv_inisial"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerInParent="true"
                                            android:fontFamily="@font/avenir_next_bold"
                                            android:text="-"
                                            android:textColor="@color/colorTextWhite"
                                            android:textSize="@dimen/size_default_initial" />
                                    </RelativeLayout>

                                    <LinearLayout
                                        android:id="@+id/ll_logo"
                                        android:layout_width="36dp"
                                        android:layout_height="36dp"
                                        android:layout_gravity="center_vertical"
                                        android:background="@drawable/round_history"
                                        android:orientation="vertical"
                                        android:visibility="gone">

                                        <ImageView
                                            android:id="@+id/iv_icon"
                                            android:layout_width="36dp"
                                            android:layout_height="36dp"
                                            android:layout_gravity="center"
                                            android:clickable="true" />

                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginEnd="8dp"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/tv_nama"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:layout_marginBottom="3dp"
                                            android:fontFamily="@font/avenir_next_demi"
                                            android:text="-"
                                            android:textColor="@color/black3"
                                            android:textSize="13dp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:id="@+id/tv_norek"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:fontFamily="@font/avenir_next_medium"
                                            android:text="-"
                                            android:textColor="@color/black3"
                                            android:textSize="13dp" />
                                    </LinearLayout>

                                </LinearLayout>
                            </LinearLayout>


                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <View
                    android:id="@+id/view"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_below="@id/lin1"
                    android:alpha="0.14"
                    android:background="@drawable/drop_shadow_konfirmasi" />

                <LinearLayout
                    android:id="@+id/lin2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/view"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="0dp"
                        android:paddingEnd="@dimen/_20sdp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/avenir_next_bold"
                                android:text="Detail"
                                android:textColor="@color/colorMenuBottomNavigation"
                                android:textSize="@dimen/size_konfirmasi_subtitle"
                                android:textStyle="bold" />


                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_detail_payment"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:overScrollMode="never" />


                            <View
                                android:id="@+id/view3"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:background="@color/white" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>



                </LinearLayout>

                <View
                    android:id="@+id/view2"
                    android:layout_width="match_parent"
                    android:layout_height="6dp"
                    android:layout_below="@id/lin2"
                    android:alpha="0.14"
                    android:background="@drawable/drop_shadow_konfirmasi" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/view2"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="0dp"
                        android:paddingEnd="@dimen/_20sdp">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="16dp"
                            android:orientation="vertical">
                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/notif_color"
                                android:fontFamily="@font/avenir_next_bold"
                                android:textSize="16dp"
                                android:text="Kode Verifikasi"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black3"
                                android:fontFamily="@font/avenir_next_medium"
                                android:layout_marginTop="12dp"
                                android:textSize="14dp"
                                android:text="@string/text_tartun_detail"/>


                            <Button
                                android:id="@+id/btnVerifikasi"
                                android:layout_width="match_parent"
                                android:layout_height="104dp"
                                android:layout_marginTop="16dp"
                                android:layout_marginBottom="20dp"
                                android:textAllCaps="false"
                                android:textSize="24dp"
                                android:textColor="@color/system30"
                                android:fontFamily="@font/avenir_next_bold"
                                android:background="@drawable/bg_blue_border"/>


                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="24dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="Batas waktu kode verifikasi"
                                    android:textColor="@color/system30"
                                    android:textSize="@dimen/size_default_12sp" />
                                <TextView
                                    android:id="@+id/txt_timer"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:fontFamily="@font/avenir_next_demi"
                                    android:layout_marginEnd="10dp"
                                    android:textColor="@color/system30"
                                    android:text="0 Menit 00 Detik"
                                    android:textSize="16dp"
                                    />
                            </RelativeLayout>

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>



                </LinearLayout>


            </RelativeLayout>
        </ScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorButtonGrey"
        android:orientation="vertical">


        <Button
            android:id="@+id/btnSubmit"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:background="@drawable/rounded_button_blue"
            android:enabled="true"
            android:fontFamily="@font/avenir_next_bold"
            android:text="OK"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="16dp" />
    </LinearLayout>
</RelativeLayout>