<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="500dp"
    android:layout_gravity="center"
    android:background="@color/transparent"
    android:gravity="center">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:cardCornerRadius="7.5dp"
            app:cardElevation="15dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_close_tutorial"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/_6sdp"
                    android:layout_marginEnd="@dimen/_6sdp"
                    android:src="@drawable/ic_close_black_24dp"
                    app:tint="#edf1f5" />

                <LinearLayout
                    android:id="@+id/ll_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/iv_close_tutorial"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="@string/cara_mengetahui"
                        android:textColor="@color/black3"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_5sdp"
                                android:fontFamily="@font/avenir_next_medium"
                                android:text="1."
                                android:textColor="@color/black3"
                                android:textSize="12dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/avenir_next_medium"
                                android:text="@string/step_1_bpjs"
                                android:textColor="@color/black3"
                                android:textSize="12dp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_5sdp"
                                android:fontFamily="@font/avenir_next_medium"
                                android:text="2."
                                android:textColor="@color/black3"
                                android:textSize="12dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/avenir_next_medium"
                                android:text="@string/step_2_bpjs"
                                android:textColor="@color/black3"
                                android:textSize="12dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <ImageView
                        android:layout_width="@dimen/_160sdp"
                        android:layout_height="@dimen/_100sdp"
                        android:layout_gravity="center"
                        android:layout_marginVertical="@dimen/_10sdp"
                        android:src="@drawable/img_bpjs"></ImageView>
                </LinearLayout>
            </RelativeLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</FrameLayout>