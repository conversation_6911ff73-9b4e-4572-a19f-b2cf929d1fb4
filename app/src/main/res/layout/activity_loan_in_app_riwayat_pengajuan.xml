<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.loaninappcc.LoanInAppRiwayatPengajuanActivity">

    <include
        android:id="@+id/tb_riwayat_pengajuan"
        layout="@layout/toolbar_not_elavated_revamp"/>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/tb_riwayat_pengajuan">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/ll_container_investasi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/primary_blue80"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_history_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:orientation="horizontal"
                            android:overScrollMode="never"/>
                    </LinearLayout>

                    <androidx.core.widget.NestedScrollView
                        android:id="@+id/nested_scrollview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@+id/ll_container_investasi"
                        android:fillViewport="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_history"
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="visible"
                                android:overScrollMode="never"/>
                            
                            <LinearLayout
                                android:id="@+id/ll_empty_data"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone"
                                android:orientation="vertical">
                                
                                <ImageView
                                    android:layout_width="@dimen/space_x33_half"
                                    android:layout_height="@dimen/space_x32_half"
                                    android:background="@drawable/ic_history_empty"
                                    android:layout_gravity="center_horizontal"
                                    android:layout_marginTop="@dimen/space_x8_half"
                                    android:layout_marginHorizontal="@dimen/space_x4"/>

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    style="@style/Body2MediumText.Bold.NeutralDark40"
                                    android:text="@string/txt_riwayat_tidak_ditemukan"
                                    android:layout_marginTop="@dimen/space_x1"
                                    android:layout_marginHorizontal="@dimen/space_x2"
                                    android:textAlignment="center"/>

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    style="@style/Body3SmallText.Regular.NeutralDark10"
                                    android:text="@string/desc_riwayat_tidak_ditemukan"
                                    android:layout_marginTop="@dimen/space_x1"
                                    android:layout_marginHorizontal="@dimen/space_x2"
                                    android:textAlignment="center"/>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.core.widget.NestedScrollView>
                </RelativeLayout>
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>