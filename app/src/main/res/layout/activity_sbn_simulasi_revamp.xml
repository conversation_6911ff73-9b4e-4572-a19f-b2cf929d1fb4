<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/rl_parent"
    tools:context=".ui.activities.sbnrevamp.SbnSimulasiRevampActivity">

    <include
        android:id="@+id/tb_simulasi_sbn"
        layout="@layout/toolbar_revamp_no_elevation" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_beli_sbn"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:layout_above="@+id/ll_button"
        android:layout_below="@+id/tb_simulasi_sbn">
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/ll_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginHorizontal="@dimen/space_x2">
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_x2">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_x1"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:layout_weight="1">

                                    <LinearLayout
                                        android:id="@+id/ll_logo"
                                        android:layout_width="@dimen/space_x5"
                                        android:layout_height="@dimen/space_x5"
                                        android:layout_gravity="center_vertical"
                                        android:gravity="center_vertical"
                                        android:background="@drawable/round_history"
                                        android:orientation="vertical"
                                        android:visibility="visible">

                                        <ImageView
                                            android:id="@+id/iv_icon"
                                            android:layout_width="match_parent"
                                            android:layout_height="32dp"
                                            android:layout_gravity="center"/>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:orientation="vertical"
                                        android:layout_marginStart="@dimen/space_x1"
                                        android:padding="@dimen/size_2dp"
                                        android:layout_marginEnd="@dimen/space_x2">

                                        <TextView
                                            android:id="@+id/tv_title_source"
                                            style="@style/Body2MediumText.Bold.NeutralDark40"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:paddingVertical="@dimen/size_3dp"/>

                                        <TextView
                                            android:id="@+id/tv_subtitle_source"
                                            style="@style/Caption1SmallText.Medium.NeutralLight80"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"/>
                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/ll_status"
                                    android:layout_width="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:visibility="gone"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/tv_status"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginVertical="@dimen/space_half"
                                        android:layout_marginHorizontal="@dimen/space_x1"
                                        android:textColor="@color/black3"
                                        android:textAlignment="center"
                                        style="@style/Caption1SmallText.SemiBold.NeutralDark40" />
                                </LinearLayout>
                            </LinearLayout>

                        </RelativeLayout>

                        <View
                            android:id="@+id/view1"
                            android:layout_width="match_parent"
                            android:layout_height="2dp"
                            android:layout_marginVertical="@dimen/space_x2"
                            android:background="@color/neutralLight20" />
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_detail_payment"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:overScrollMode="never" />
                        </RelativeLayout>


                    </LinearLayout>
                    <View
                        android:id="@+id/view2"
                        android:layout_width="match_parent"
                        android:layout_height="6dp"
                        android:layout_marginVertical="@dimen/space_x1"
                        android:background="@color/neutralLight10" />
                    <RelativeLayout
                        android:id="@+id/rl_input_nominal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/space_x2">

                        <TextView
                            android:id="@+id/tv_label_nominal"
                            style="@style/Body3SmallText.SemiBold.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Nominal Investasi" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_nominal"
                            style="@style/TextInputLayoutRevamp.NoBorder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/tv_label_nominal"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_half"
                            android:background="@drawable/background_cardview_stroked"
                            android:elevation="@dimen/size_1dp"
                            android:paddingVertical="@dimen/space_x1_half"
                            app:endIconMode="clear_text"
                            app:expandedHintEnabled="true"
                            app:helperTextTextColor="@color/neutral_light80"
                            app:hintAnimationEnabled="false"
                            app:hintEnabled="false"
                            app:hintTextColor="@color/neutral_light80"
                            app:prefixText="Rp"
                            app:prefixTextAppearance="@style/Title4Text.Bold.NeutralDark40"
                            app:prefixTextColor="@color/neutral_dark40">

                            <id.co.bri.brimo.ui.customviews.edittext.KeyboardEditText
                                android:id="@+id/et_nominal"
                                style="@style/Title4Text.Bold.NeutralDark40"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/rupiah_0"
                                android:inputType="number"
                                android:maxLines="1"
                                android:maxLength="15"
                                android:textColorHint="@color/neutral_dark40"
                                app:hintTextColor="@color/neutral_dark40" />

                        </com.google.android.material.textfield.TextInputLayout>
                    </RelativeLayout>
                    <LinearLayout
                        android:id="@+id/ll_aditional_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginVertical="@dimen/space_x1"
                            app:cardCornerRadius="@dimen/space_x1"
                            android:orientation="vertical"
                            android:background="@drawable/background_cardview_stroked">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">
                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:padding="@dimen/space_x1"
                                    android:layout_marginStart="@dimen/space_x2"
                                    android:layout_marginTop="@dimen/space_x1_half">
                                    <TextView
                                        android:id="@+id/tv_imbal_perbulan"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        style="@style/Body3SmallText.Medium.NeutralLight60" />
                                    <TextView
                                        android:id="@+id/tv_harga_imbal"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentEnd="true"
                                        style="@style/Body2MediumText.Bold.SematicGreen80" />
                                </RelativeLayout>
                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:background="@color/light_10">
                                    <TextView
                                        android:id="@+id/tv_desc_imbal"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:padding="@dimen/space_x2"
                                        android:layout_marginHorizontal="@dimen/space_x1"
                                        style="@style/Body3SmallText.Medium.NeutralLight60" />
                                </RelativeLayout>
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>
                        <View
                            android:id="@+id/view3"
                            android:layout_width="match_parent"
                            android:layout_height="6dp"
                            android:layout_marginVertical="@dimen/space_x2"
                            android:background="@color/neutralLight10"
                            android:layout_marginHorizontal="@dimen/space_x1"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:orientation="horizontal">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/txt_title_simulasi_investasi"
                                style="@style/Body3SmallText.Medium.NeutralLight60"/>
                            <TextView
                                android:id="@+id/tv_nilai_investasi"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_half"
                                style="@style/Body3SmallText.Bold.NeutralLight60"/>
                        </LinearLayout>

                        <androidx.cardview.widget.CardView
                            android:id="@+id/cv_additional_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:layout_marginVertical="@dimen/space_x1"
                            app:cardCornerRadius="@dimen/space_x1"
                            android:orientation="vertical"
                            android:background="@drawable/background_cardview_stroked">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/light_10"
                                android:orientation="vertical">
                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:padding="@dimen/space_x1"
                                    android:layout_marginStart="@dimen/space_x2"
                                    android:layout_marginTop="@dimen/space_x1_half">
                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/rv_amount_payment"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:overScrollMode="never" />
                                </RelativeLayout>
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:background="@color/success10">
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingStart="@dimen/space_x2"
                                        android:paddingTop="@dimen/space_x2"
                                        android:layout_marginHorizontal="@dimen/space_x1"
                                        style="@style/Body3SmallText.Medium.NeutralLight60"
                                        android:text="Estimasi Total Investasi"/>

                                    <TextView
                                        android:id="@+id/tv_total_estimasi"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingStart="@dimen/space_x2"
                                        android:layout_marginHorizontal="@dimen/space_x1"
                                        android:layout_marginBottom="@dimen/space_x2"
                                        style="@style/Title4Text.Bold.SematicGreen80"/>
                                </LinearLayout>
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <WebView
                        android:id="@+id/wv_information"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </LinearLayout>
        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    </androidx.core.widget.NestedScrollView>
    <LinearLayout
        android:id="@+id/ll_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/neutralLight10"
        android:orientation="vertical"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSubmit"
            style="@style/Body2MediumText.Bold.NeutralLight10"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            android:layout_marginTop="@dimen/space_x2"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x2"
            android:background="@drawable/button_primary_bg"
            android:text="@string/beli"
            android:letterSpacing="0"
            android:textAllCaps="false" />
    </LinearLayout>
</RelativeLayout>