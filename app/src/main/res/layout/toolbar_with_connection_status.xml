<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:textAlignment="center"
    android:elevation="@dimen/_4sdp"
    android:outlineProvider="bounds"
    android:background="@color/toolbar_blue"
    app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/avenir_next_bold"
        android:textSize="16dp"
        style="@style/ToolbarTitleStyle"
        android:id="@+id/textTitle"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit ="marquee_forever"
        android:scrollHorizontally="true"/>

    <LinearLayout
        android:layout_width="@dimen/space_x5"
        android:layout_height="@dimen/space_x5"
        android:gravity="center"
        android:layout_gravity="end"
        android:layout_marginRight="@dimen/space_half">

        <ImageButton
            android:id="@+id/img_internet_indicator"
            android:layout_width="@dimen/space_x2"
            android:layout_height="@dimen/space_x2"
            android:layout_gravity="center"
            android:visibility="visible"/>

    </LinearLayout>
</androidx.appcompat.widget.Toolbar>