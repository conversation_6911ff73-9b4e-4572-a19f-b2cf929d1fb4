<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.activities.ccqrismpm.SofQrisActivity">

    <include
        android:id="@+id/tb_receipt"
        layout="@layout/toolbar_not_elevated" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="15dp"
                            android:layout_marginTop="15dp"
                            app:cardBackgroundColor="@color/primary_blue10"
                            app:cardCornerRadius="4dp"
                            app:cardElevation="0dp"
                            app:strokeColor="@color/highlightColor"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:id="@+id/ly_alert_blue"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="15dp">

                                <ImageView
                                    android:layout_width="15dp"
                                    android:layout_height="15dp"
                                    android:layout_marginTop="3dp"
                                    android:src="@drawable/info_biru" />

                                <TextView
                                    android:id="@+id/text_view_alert"
                                    style="@style/Body2MediumText.SemiBold.NeutralDark20"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="10dp"
                                    android:text=""
                                    android:textSize="12dp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:id="@+id/cc_title"
                            style="@style/Title1Text.Bold.NeutralDark40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="15dp"
                            android:layout_marginTop="15dp"
                            android:text="@string/kartuKredit"
                            android:textSize="18dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_cc"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="1"
                            tools:listitem="@layout/item_cc_qris_mpm" />

                        <TextView
                            android:id="@+id/savings_title"
                            style="@style/Title1Text.Bold.NeutralDark40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="15dp"
                            android:layout_marginTop="15dp"
                            android:text="@string/toolbar_tabungan"
                            android:textSize="18dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_savings"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="3"
                            tools:listitem="@layout/item_sumber_dana_new" />
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_confirm"
                style="@style/ButtonPrimaryRevamp"
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_x6"
                android:layout_marginHorizontal="15dp"
                android:layout_marginVertical="10dp"
                android:enabled="false"
                android:text="@string/set_to_be_sof"
                android:textAllCaps="false"
                android:textColor="@color/neutralLight40" />

        </LinearLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>