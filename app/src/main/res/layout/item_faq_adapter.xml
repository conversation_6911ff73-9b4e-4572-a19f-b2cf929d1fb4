<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@color/whiteColor"
    android:layout_height="wrap_content">

    <id.co.bri.brimo.ui.widget.expandablelayout.ExpandableLayout
        android:id="@+id/expandable_layout"
        app:expWithParentScroll="true"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/firstLayer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <TextView
                            android:id="@+id/tv_title_faq"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@+id/ll_arrow"
                            style="@style/Body3SmallText.SemiBold.NeutralDark40"
                            android:text="Bagaimana Perhitungan Total Investasi dilakukan?"/>

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_panah"
                        android:layout_width="@dimen/space_x3_half"
                        android:layout_height="@dimen/space_x3_half"
                        android:src="@drawable/ic_arrow_down_blue"
                        android:layout_marginVertical="@dimen/space_x2"
                        android:layout_marginHorizontal="@dimen/space_x1"/>

                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ly_view"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:orientation="horizontal"
                android:background="@color/neutralLight20"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginBottom="@dimen/space_half"
            android:background="@color/light_10"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_desc_faq"
                style="@style/Caption1SmallText.Medium.NeutralLight60"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:layout_marginVertical="@dimen/space_x2" />

        </LinearLayout>


    </id.co.bri.brimo.ui.widget.expandablelayout.ExpandableLayout>

</LinearLayout>