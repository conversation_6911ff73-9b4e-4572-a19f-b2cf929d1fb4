<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/space_x2"
    android:paddingVertical="@dimen/space_x1_half"
    android:background="@drawable/bg_selected_account"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Body2MediumText.Regular.NeutralBaseWhite"
        android:text="@string/selected_account"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/iv_card"
        android:layout_width="@dimen/space_x8"
        android:layout_height="@dimen/space_x2_half"
        android:src="@drawable/img_britama_bisnis"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_card_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/CaptionText.Medium.White"
        app:layout_constraintTop_toBottomOf="@id/iv_card"
        android:text="@string/empty"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/space_half"/>

</androidx.constraintlayout.widget.ConstraintLayout>