<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/space_x2"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/space_x1_half"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_negara"
            android:layout_width="@dimen/space_x2_half"
            android:layout_height="@dimen/space_x2_half" />

        <TextView
            android:id="@+id/tv_negara"
            style="@style/Body2MediumText.SemiBold.NeutralBaseBlack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x1"
            android:maxLines="2"
            android:text="-" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/neutralLight20" />
</LinearLayout>