<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    android:background="@color/colorPrimaryDark">

    <include
        android:id="@+id/tb_konfirmasi_pulsa"
        layout="@layout/toolbar"/>
    <View
        android:id="@+id/tb_shadow"
        android:layout_below="@+id/tb_konfirmasi_pulsa"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:background="@drawable/toolbar_dropshadow" />
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_below="@id/tb_konfirmasi_pulsa"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorButtonGrey">
        <LinearLayout
            android:layout_below="@+id/tb_shadow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">



            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="@dimen/_5sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="17dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="Sumber Dana"
                        android:textColor="@color/colorButtonBlue"
                        android:textSize="14dp" />

                    <RelativeLayout
                        android:id="@+id/item_layout_background"
                        android:layout_width="match_parent"
                        android:layout_height="70dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="17dp"
                        android:background="@drawable/rounded_button_konfirmasi_pulsa">

                        <!--                    <Spinner-->
                        <!--                        android:id="@+id/spiner_konfirmasi"-->
                        <!--                        android:layout_width="fill_parent"-->
                        <!--                        android:layout_height="wrap_content"-->
                        <!--                        android:background="@null"-->
                        <!--                        android:minHeight="0dp"-->
                        <!--                        android:textSize="16dp" />-->

                        <RelativeLayout
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            android:layout_marginTop="12dp"
                            android:layout_marginStart="15dp"
                            android:orientation="vertical"
                            android:backgroundTint="@color/colorButtonBlue"
                            android:background="@drawable/round_button">

                            <ImageView
                                android:id="@+id/iv_inisial"
                                android:layout_width="@dimen/_20sdp"
                                android:layout_height="@dimen/_20sdp"
                                android:layout_margin="10dp"
                                android:layout_gravity="center" />
                            <TextView
                                android:id="@+id/tv_inisial"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/avenir_next_bold"
                                android:textColor="@color/colorTextWhite"
                                android:textSize="@dimen/size_default_initial"
                                android:layout_centerInParent="true"
                                android:text=""/>

                        </RelativeLayout>

                        <LinearLayout

                            android:gravity="center_vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="20dp"
                            android:layout_marginStart="75dp"
                            >

                            <TextView
                                android:id="@+id/tv_norek_konfirmasi"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14dp"
                                android:textStyle="bold"
                                android:fontFamily="@font/avenir_next_bold" />

                            <TextView
                                android:id="@+id/tv_saldo_konfirmasi"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12dp"
                                android:fontFamily="@font/avenir_next_medium" />

                        </LinearLayout>


                        <ImageView
                            android:layout_width="@dimen/_10sdp"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_alignParentBottom="true"
                            android:layout_marginEnd="@dimen/_10sdp"
                            android:src="@drawable/arrow_back" />
                    </RelativeLayout>


                    <!--        <androidx.appcompat.widget.AppCompatSpinner-->
                    <!--                    android:id="@+id/spiner_konfirmasi"-->
                    <!--                    android:background="@drawable/rounded_button_konfirmasi"-->
                    <!--                    android:layout_marginTop="@dimen/_15sdp"-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="@dimen/_55sdp">-->

                    <!--                </androidx.appcompat.widget.AppCompatSpinner>-->

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="10dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="Nomor Tujuan"
                        android:textColor="@color/colorButtonBlue"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            android:background="@drawable/round_button"
                            android:backgroundTint="#eef2f6"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/img_operator"
                                android:layout_width="35dp"
                                android:layout_height="35dp"
                                android:layout_gravity="center"
                                android:layout_margin="5dp"
                                android:clickable="true"
                                android:src="@drawable/provider_simpati" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="20dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_nohp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_15sdp"
                                android:fontFamily="@font/avenir_next_demi"
                                android:textColor="@color/black3"
                                android:textSize="@dimen/size_default_list1"
                                android:text="-" />

                            <TextView
                                android:id="@+id/tv_operator"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_15sdp"
                                android:layout_marginTop="@dimen/_5sdp"
                                android:fontFamily="@font/avenir_next_medium"
                                android:textColor="@color/black3"
                                android:textSize="@dimen/size_default_list2"
                                android:text="-" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginTop="@dimen/_15sdp"
                        android:layout_marginBottom="@dimen/_10sdp"
                        android:visibility="gone"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="44dp"
                            android:layout_height="44dp"
                            android:backgroundTint="#eef2f6"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/iv_konfirmasi_kategori"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_gravity="center_vertical"
                                android:layout_margin="10dp"
                                android:clickable="true"
                                android:src="@drawable/hp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_15sdp"
                                android:textColor="@color/black3"
                                android:fontFamily="@font/avenir_next_medium"
                                android:textSize="@dimen/size_default_list1"
                                android:text="Kategori" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/ti_konfirmasi_kategori"
                                android:layout_width="@dimen/_220sdp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/_15sdp"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false">

                                <EditText
                                    android:id="@+id/et_KategoriKonfirmasi"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:background="@null"
                                    android:textStyle="bold"
                                    android:drawableRight="@drawable/ic_chevron_right_black_24dp"
                                    android:focusable="false"
                                    android:hint="@string/hintkategori"
                                    android:maxLines="1"
                                    android:textSize="@dimen/size_default_list2"
                                    android:paddingBottom="10dp"
                                    />
                            </com.google.android.material.textfield.TextInputLayout>
                            <!--                        <TextView-->
                            <!--                            android:layout_width="wrap_content"-->
                            <!--                            android:layout_height="wrap_content"-->
                            <!--                            android:fontFamily="@font/avenir_next_medium"-->
                            <!--                            android:layout_marginTop="@dimen/_5sdp"-->
                            <!--                            android:layout_marginStart="@dimen/_15sdp"-->
                            <!--                            android:text="Pulsa"/>-->
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="17dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="Detail"
                        android:textColor="@color/colorButtonBlue"
                        android:textSize="13dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_detail_pulsa"
                        android:layout_marginTop="15dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        >


                    </androidx.recyclerview.widget.RecyclerView>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="9dp"
                        android:layout_marginBottom="9dp"
                        android:background="#bbb" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_total_pulsa"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        >

                    </androidx.recyclerview.widget.RecyclerView>

                </LinearLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>



    <Button
        android:id="@+id/btnlanjutKonfirmasi"
        android:layout_width="match_parent"
        android:layout_height="50.5dp"
        android:textAllCaps="false"
        android:layout_marginTop="@dimen/_20sdp"
        android:layout_marginStart="@dimen/_15sdp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/_15sdp"
        android:fontFamily="@font/avenir_next_bold"
        android:textSize="16dp"
        android:translationY="-16dp"
        android:background="@drawable/rounded_button_blue"
        android:backgroundTint="@color/colorButtonBlue"
        android:textColor="@color/colorTextWhite"
        android:text="Beli"/>

</RelativeLayout>