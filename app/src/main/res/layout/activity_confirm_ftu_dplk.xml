<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.dplkrevamp.ConfirmFtuDplkActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_revamp_no_elevation" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_title_sumber_dana"
                style="@style/Body2MediumText.Bold.NeutralDark40"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginTop="@dimen/space_x2"
                android:text="@string/txt_sumber_dana" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_sumber_dana"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_title_sumber_dana"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:layout_marginTop="@dimen/space_x1"
                android:layout_marginBottom="@dimen/space_x1_half"
                app:cardCornerRadius="@dimen/space_x1">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/iv_bg"
                        android:layout_width="110dp"
                        android:layout_height="110dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/bg_sumber_dana_new" />

                    <ImageView
                        android:id="@+id/iv_icon_rek"
                        android:layout_width="75dp"
                        android:layout_height="25dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/space_x1_half"
                        android:src="@drawable/ic_menu_qna_rencana" />

                    <TextView
                        android:id="@+id/tv_no_rek"
                        style="@style/Body3SmallText.Medium.NeutralDark40"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_x1"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:layout_toEndOf="@id/iv_bg" />

                    <TextView
                        android:id="@+id/tv_alias_rek"
                        style="@style/Caption1SmallText.Medium.NeutralDark10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_no_rek"
                        android:layout_marginStart="@dimen/space_x1"
                        android:layout_marginTop="@dimen/space_half"
                        android:layout_toEndOf="@id/iv_bg" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/tv_alias_rek"
                        android:layout_toEndOf="@id/iv_bg"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/iv_alert_saldo"
                            android:layout_width="@dimen/size_18dp"
                            android:layout_height="@dimen/size_18dp"
                            android:layout_marginStart="@dimen/space_x1"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginEnd="@dimen/space_half"
                            android:layout_marginBottom="@dimen/space_x1_half"
                            android:src="@drawable/ic_alert_error"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tv_saldo_rek"
                            style="@style/Body1LargeText.Bold.NeutralDark40"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/space_x1"
                            android:layout_marginTop="@dimen/space_half"
                            android:layout_marginEnd="@dimen/space_x1_half"
                            android:layout_marginBottom="@dimen/space_x1"
                            android:paddingVertical="@dimen/size_2dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_rek_utama"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:background="@drawable/bg_rek_utama"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            style="@style/Caption1SmallText.Bold.NeutralBaseWhite"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x1_half"
                            android:text="@string/txt_utama"
                            android:layout_marginVertical="@dimen/space_half" />
                    </LinearLayout>
                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <View
                android:id="@+id/view_differ"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_below="@id/cv_sumber_dana"
                android:layout_marginTop="18dp"
                android:background="@color/neutralLight20" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_transaction_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/view_differ"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                tools:itemCount="5"
                tools:listitem="@layout/item_transaction_data" />

            <LinearLayout
                android:id="@+id/layout_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btn_confirm"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_margin="15dp"
                    android:background="@drawable/rounded_button_blue"
                    android:fontFamily="@font/avenir_next_bold"
                    android:text="@string/txt_title_confirm_open_dplk"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>
        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>