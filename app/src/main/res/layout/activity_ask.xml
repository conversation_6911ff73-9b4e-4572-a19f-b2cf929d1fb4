<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/ly1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@id/ly_buka_rekening"
                android:layout_weight="0.5">

                <ImageView
                    android:id="@+id/bg_onboarding"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="@dimen/space_x16"
                    android:scaleType="fitXY"
                    android:src="@drawable/bg_onboarding_brimo" />

                <RelativeLayout
                    android:id="@+id/relative_top"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_centerInParent="true"
                        android:id="@+id/icon"
                        android:layout_width="@dimen/space_x5"
                        android:layout_height="@dimen/space_x5"
                        android:src="@drawable/brimo_logo_putih" />

                    <id.co.bri.brimo.ui.customviews.switchbutton.SwitchLanguageButtonView
                        android:id="@+id/switch_button"
                        android:layout_centerVertical="true"
                        android:layout_width="75dp"
                        android:layout_height="40dp"/>

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/ll_kontak_kami"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/space_x2"
                    android:layout_gravity="end"
                    android:background="@drawable/background_cardview_noborder_half"
                    android:backgroundTint="@color/blue_BRI80"
                    android:orientation="horizontal"
                    android:padding="@dimen/space_half">

                    <ImageView
                        android:layout_width="@dimen/space_x3"
                        android:layout_height="@dimen/space_x3"
                        android:contentDescription="@string/information"
                        android:src="@drawable/ic_call_center_outline" />

                    <TextView
                        style="@style/Caption2MicroText.Bold.NeutralBaseWhite"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_2dp"
                        android:text="@string/contact_us_fastmenu" />
                </LinearLayout>

                <id.co.bri.brimo.ui.customviews.AutoScrollViewPager
                    android:id="@+id/vp_rdn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/relative_top" />

                <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                    android:id="@+id/dots_indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:paddingBottom="@dimen/space_x5"
                    app:dotsColor="#88BFEC"
                    app:dotsCornerRadius="8dp"
                    app:dotsSize="10dp"
                    app:dotsSpacing="4dp"
                    app:dotsWidthFactor="2.5"
                    app:progressMode="false"
                    app:selectedDotColor="@color/highlightColor" />
            </RelativeLayout>

            <!--Button Section-->
            <LinearLayout
                android:id="@+id/ly_buka_rekening"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentBottom="true"
                android:layout_weight="0.7"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/Headline5Text.Bold.BlueLight"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/tv_desc"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginBottom="@dimen/space_x1"
                    android:textAlignment="center"
                    tool:text="xxxxxxx" />

                <TextView
                    android:id="@+id/tv_desc"
                    style="@style/BodyMediumText.Medium.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:textAlignment="center"
                    tool:text="XXXXXXXXXXXXXXXXXXX" />

                <LinearLayout
                    android:id="@+id/ly_button"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnYa"
                        style="@style/ButtonPrimaryRevamp"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_x6"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:text="@string/punya_akun_brimo" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnTidak"
                        style="@style/ButtonPrimaryRevampOutline"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_x6"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/space_x4"
                        android:text="@string/belum_punya_akun_brimo" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>

