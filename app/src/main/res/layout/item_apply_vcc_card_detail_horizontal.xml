<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/size_2dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/space_x1_half"
    android:paddingVertical="@dimen/space_x1">

    <TextView
        android:id="@+id/tvItemApplyVccCardDetail2Title"
        style="@style/Body3SmallText.Medium.NeutralDark10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/space_x1_half"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="2"
        tools:text="@tools:sample/lorem[5]" />

    <TextView
        android:id="@+id/tvItemApplyVccCardDetail2Desc"
        style="@style/Body3SmallText.Medium.NeutralDark40"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAlignment="textEnd"
        tools:text="@tools:sample/lorem[1]" />
</LinearLayout>