<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/_2sdp"
    android:layout_marginBottom="@dimen/_3sdp"
    android:background="@color/neutral_baseWhite"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginTop="@dimen/space_x1">

        <ImageView
            android:id="@+id/iv_title"
            android:layout_width="@dimen/space_x4"
            android:layout_height="@dimen/space_x4"
            android:layout_centerVertical="true"
            android:contentDescription="@null"
            android:padding="@dimen/space_half"
            android:src="@drawable/ic_bus_front" />

        <TextView
            android:id="@+id/tv_jenis_travel"
            style="@style/Body3SmallText.SemiBold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/space_x1"
            android:layout_marginEnd="@dimen/space_half"
            android:layout_toEndOf="@+id/iv_title"
            android:layout_toStartOf="@id/tv_pulang_pergi"
            android:gravity="center_vertical"
            android:textColor="@color/black3" />

        <TextView
            android:id="@+id/tv_pulang_pergi"
            style="@style/Caption2MicroText.SemiBold.NeutralBaseWhite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_radius4"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:text="@string/txt_pulang_pergi" />

    </RelativeLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginVertical="@dimen/space_x1_half"
        android:background="@color/accent3Color" />

    <RelativeLayout
        android:id="@+id/rl_tujuan_travel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginBottom="@dimen/space_x2">

        <TextView
            android:id="@+id/tv_tujuan_travel"
            style="@style/Body3SmallText.Medium.NeutralDark40"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="@dimen/space_half"
            android:layout_toStartOf="@id/tv_pergi"
            android:gravity="center_vertical"
            tools:text="Jakarta" />

        <TextView
            android:id="@+id/tv_pergi"
            style="@style/Caption2MicroText.SemiBold.NeutralBaseWhite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_radius4"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:text="@string/txt_pergi"
            android:visibility="gone"/>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/space_x1"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                style="@style/Body3SmallText.Medium.NeutralLight80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="@dimen/space_x1"
                android:text="@string/waktu_keberangkatan" />

            <TextView
                android:id="@+id/tv_desc_keberangkatan"
                style="@style/Body3SmallText.Medium.NeutralDark40"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="@dimen/space_half"
                android:layout_marginEnd="@dimen/space_x1" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title_armada"
                style="@style/Body3SmallText.Medium.NeutralLight80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:text="@string/nama_kereta"
                android:textAlignment="textEnd" />

            <TextView
                android:id="@+id/tv_desc_armada"
                style="@style/Body3SmallText.Medium.NeutralDark40"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/space_half"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="textEnd" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginVertical="@dimen/space_x1_half"
        android:background="@color/accent3Color" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginBottom="@dimen/space_x2"
        android:baselineAligned="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="@dimen/space_x1"
            android:layout_toStartOf="@id/btn_lihat"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title_booking_invoice"
                style="@style/Body3SmallText.Medium.NeutralLight80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:text="@string/no_invoice" />

            <TextView
                android:id="@+id/tv_desc_booking_invoice"
                style="@style/Body3SmallText.Medium.NeutralDark40"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="@dimen/space_half"
                android:maxLines="2" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_lihat"
            style="@style/ButtonPrimaryRevampMaterial"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/size_32dp"
            android:layout_gravity="end"
            android:letterSpacing="0"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:paddingHorizontal="@dimen/space_x1"
            android:paddingVertical="@dimen/space_x1"
            android:text="@string/lihat_detail"
            android:textSize="@dimen/text_caption1"
            tools:ignore="SpUsage" />

    </RelativeLayout>

</LinearLayout>
