<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentId"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:layout_width="@dimen/space_x6"
        android:layout_height="@dimen/space_half"
        android:background="@color/accent3Color"
        android:layout_gravity="top|center"
        android:layout_marginTop="@dimen/space_x1"
        android:layout_marginBottom="@dimen/space_x3_half"/>

    <LinearLayout
        android:layout_gravity="center_vertical"
        android:id="@+id/ly_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ly_buka_rekening"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/ly_buka_rekening"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="@dimen/_210sdp"
            android:layout_height="@dimen/_210sdp"
            android:id="@+id/iv_image"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/space_x3"
            android:src="@drawable/ic_verifikasi_rdn" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/space_x2"
            android:id="@+id/tv_title"
            android:layout_marginTop="@dimen/space_x1"
            android:layout_marginRight="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x1_half"
            style="@style/SubTitleText.Bold"
            android:textColor="@color/neutral_dark40"
            android:text="RDN Sedang dalam Proses Verifikasi"
            android:textAlignment="center"/>

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/space_x2"
            android:layout_marginRight="@dimen/space_x2"
            style="@style/Body2MediumText.Medium.NeutralDark10"
            android:text="Kamu baru bisa top up setelah proses verifikasi selesai dan RDN sudah aktif. Cek berkala status RDN-mu dan kembali ke sini untuk top up, ya!"
            android:textAlignment="center" />

        <Button
            android:id="@+id/btnSubmit"
            style="@style/BodyMediumText.Bold.White"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            android:layout_margin="@dimen/space_x2"
            android:background="@drawable/rounded_button_blue"
            android:text="Mengerti"
            android:layout_alignParentBottom="true"
            android:textAllCaps="false" />
    </LinearLayout>

</FrameLayout>