<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorKartuGrey"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.ssc.PengaduanTrxGagalActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/layout_pengajuan_terakhir"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:background="@color/white"
                    android:elevation="5dp"
                    android:outlineAmbientShadowColor="@color/colorOtpBox"
                    android:outlineSpotShadowColor="@color/colorOtpBox"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="5dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/avenir_next_medium"
                            android:text="@string/dec_keluhan_gagal"
                            android:textColor="@color/black3"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="30dp"
                                    android:layout_centerVertical="true"
                                    android:layout_marginHorizontal="@dimen/_10sdp"
                                    android:src="@drawable/ic_card" />
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                tools:ignore="RtlSymmetry">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="5dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/pilih_rekening"
                                    android:textColor="@color/black3"
                                    android:textSize="14dp" />

                                <com.google.android.material.textfield.TextInputLayout
                                    android:id="@+id/ti_rekening"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:hintAnimationEnabled="false"
                                    app:hintEnabled="false">

                                    <EditText
                                        android:id="@+id/et_rekening"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@android:color/transparent"
                                        android:drawableRight="@drawable/ic_arrow_right_blue"
                                        android:focusable="false"
                                        android:fontFamily="@font/avenir_next_demi"
                                        android:inputType="none|textNoSuggestions"
                                        android:maxLines="1"
                                        android:paddingBottom="5dp"
                                        android:textColor="@color/colorAccent"
                                        android:textColorHint="@color/black3"
                                        android:textSize="16dp"
                                        android:textStyle="bold" />
                                </com.google.android.material.textfield.TextInputLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#bbb" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="17dp"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/iv_kredit"
                                    android:layout_width="32dp"
                                    android:layout_height="30dp"
                                    android:layout_centerVertical="true"
                                    android:layout_marginHorizontal="@dimen/_10sdp"
                                    android:src="@drawable/ic_tanggal" />
                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                tools:ignore="RtlSymmetry">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="5dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/choose_date"
                                    android:textColor="@color/black3"
                                    android:textSize="14dp" />

                                <com.google.android.material.textfield.TextInputLayout
                                    android:id="@+id/ti_durasi"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:hintAnimationEnabled="false"
                                    app:hintEnabled="false">

                                    <EditText
                                        android:id="@+id/et_durasi"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@android:color/transparent"
                                        android:drawableRight="@drawable/ic_arrow_right_blue"
                                        android:focusable="false"
                                        android:fontFamily="@font/avenir_next_demi"
                                        android:inputType="none|textNoSuggestions"
                                        android:maxLines="1"
                                        android:paddingBottom="5dp"
                                        android:textColor="@color/colorAccent"
                                        android:textColorHint="@color/black3"
                                        android:textSize="14dp"
                                        android:textStyle="bold" />
                                </com.google.android.material.textfield.TextInputLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#bbb" />

                        <Button
                            android:id="@+id/btn_cari"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:alpha="0.3"
                            android:background="@drawable/rounded_button_blue"
                            android:enabled="false"
                            android:fontFamily="@font/avenir_next_bold"
                            android:text="@string/search"
                            android:textAllCaps="false"
                            android:textColor="@android:color/white"
                            android:textSize="16dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_recylerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginVertical="10dp"
                    android:background="@color/white"
                    android:elevation="5dp"
                    android:outlineAmbientShadowColor="@color/colorOtpBox"
                    android:outlineSpotShadowColor="@color/colorOtpBox"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="5dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_bulan"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:descendantFocusability="blocksDescendants"
                        android:nestedScrollingEnabled="false" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/layout_notfound"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginVertical="10dp"
                    android:background="@color/white"
                    android:elevation="5dp"
                    android:outlineAmbientShadowColor="@color/colorOtpBox"
                    android:outlineSpotShadowColor="@color/colorOtpBox"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="5dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@color/white"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="32dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_complain_maaf" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:fontFamily="@font/avenir_next_bold"
                            android:text="@string/txt_sorry_no_transaction"
                            android:textAlignment="center"
                            android:textColor="@color/black3"
                            android:textSize="16dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="50dp"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/avenir_next_medium"
                            android:text="@string/txt_select_another_date_to_complaint"
                            android:textAlignment="center"
                            android:textColor="@color/black3"
                            android:textSize="14dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>