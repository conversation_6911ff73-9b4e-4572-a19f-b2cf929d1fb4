<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="@dimen/space_x1"
    app:cardBackgroundColor="@color/white"
    app:cardElevation="0dp"
    tools:context=".ui.customviews.dialog.DialogInformation">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/space_x3"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x1"
                android:src="@drawable/maaf_maaf" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Headline6Text.Bold.BlueLight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x1_half"
                android:text="@string/str_apologize"
                android:textAlignment="center" />

            <TextView
                android:id="@+id/tv_desc"
                style="@style/BodySmallText.Medium.Black"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/space_x2"
                android:text="@string/str_apologize_rtgs_info"
                android:textAlignment="center" />

            <androidx.cardview.widget.CardView
                android:id="@+id/rlKeterangan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/space_x2"
                android:gravity="bottom"
                app:cardBackgroundColor="@color/accent3Color"
                app:cardElevation="0dp"
                app:contentPadding="@dimen/space_x1">

                <TextView
                    android:id="@+id/tv_operational_time"
                    style="@style/BodyMediumText.Bold.BluePrimary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:layout_gravity="center"
                    android:drawablePadding="@dimen/space_x1"
                    android:drawableTint="@color/primaryColor"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/space_x1_half"
                    android:text="@string/operational_time_rtgs"
                    android:textAlignment="center" />


            </androidx.cardview.widget.CardView>

            <Button
                android:id="@+id/btn_ok"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="@dimen/space_x2"
                android:background="@drawable/rounded_button_blue"
                android:fontFamily="@font/avenir_next_bold"
                android:text="OK"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="16dp" />

        </LinearLayout>


    </RelativeLayout>


</androidx.cardview.widget.CardView>