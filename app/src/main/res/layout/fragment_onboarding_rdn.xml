<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentId"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_gravity="center_vertical"
        android:id="@+id/ly_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ly_buka_rekening"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/ly_buka_rekening"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="@dimen/_210sdp"
            android:layout_height="@dimen/_210sdp"
            android:id="@+id/iv_image"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/space_x3"
            android:contentDescription="description"
            android:src="@drawable/ic_rdn_empty" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/space_x4"
            android:id="@+id/tv_title"
            android:layout_marginTop="@dimen/space_x1"
            android:layout_marginRight="@dimen/space_x4"
            android:layout_marginBottom="@dimen/space_x1_half"
            style="@style/Headline5Text.Bold.White"
            android:textAlignment="center"/>

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/space_x4"
            android:layout_marginRight="@dimen/space_x4"
            style="@style/BodyMediumText.Medium.White"
            android:textAlignment="center" />
    </LinearLayout>

</FrameLayout>