<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_marginBottom="16dp"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:elevation="4dp"
        app:cardCornerRadius="8dp"
        android:id="@+id/cv_card"
        app:cardBackgroundColor="@color/white"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"
                android:layout_marginTop="18dp"
                android:id="@+id/iv_image"
                android:layout_width="40dp"
                android:src="@drawable/ic_aro_deposito"
                android:layout_height="50dp"/>

            <LinearLayout
                android:orientation="vertical"
                android:layout_toRightOf="@id/iv_image"
                android:layout_width="match_parent"
                android:layout_marginBottom="12dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="8dp"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="match_parent"
                    android:text="ARO"
                    android:id="@+id/tv_title"
                    android:textSize="16dp"
                    android:textColor="@color/black3"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/avenir_next_bold"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/tv_desc"
                    android:layout_width="match_parent"
                    android:text="Fasilitas perpanjangan deposito secara otomatis apabila masuk tanggal jatuh tempo dan deposito tidak dicairkan"
                    android:textSize="16dp"
                    android:textColor="@color/black3"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/avenir_next_medium"
                    android:layout_height="wrap_content"/>

            </LinearLayout>
        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>