<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="id.co.bri.brimo.ui.activities.dplk.OnboardingDplkActivity">

    <include
        android:id="@+id/tb_onboard_dplk"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_below="@id/tb_onboard_dplk"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_full_bri_blue">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_marginBottom="90dp"
                android:layout_height="match_parent">
                <id.co.bri.brimo.ui.customviews.AutoScrollViewPager
                    android:id="@+id/vp_rdn"
                    android:layout_alignParentTop="true"
                    android:layout_above="@+id/dots_indicator"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentStart="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                    android:id="@+id/dots_indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="18dp"
                    android:layout_marginBottom="10dp"
                    app:dotsColor="#88BFEC"
                    app:dotsCornerRadius="8dp"
                    app:dotsSize="10dp"
                    app:dotsSpacing="4dp"
                    app:dotsWidthFactor="2.5"
                    app:progressMode="false"
                    app:selectedDotColor="@color/white" />
            </RelativeLayout>


            <!--Button Section-->
            <LinearLayout
                android:id="@+id/ly_buka_rekening"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <Button
                    android:id="@+id/btn_lanjut"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:textSize="16dp"
                    android:layout_marginBottom="16dp"
                    android:textAllCaps="false"
                    android:background="@drawable/rounded_button_white"
                    android:fontFamily="@font/avenir_next_bold"
                    android:text=""
                    android:textColor="@color/colorTextBlueBri" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</RelativeLayout>