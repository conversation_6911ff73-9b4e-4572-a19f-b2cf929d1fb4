<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_cardview_neutral10_stroked"
    android:layout_marginHorizontal="@dimen/space_x2"
    android:layout_marginVertical="@dimen/space_x1_half"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:background="@drawable/background_cardview_white_stroked"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginTop="@dimen/space_x2"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_weight="1">

                <LinearLayout
                    android:id="@+id/ll_logo2"
                    android:layout_width="@dimen/space_x5"
                    android:layout_height="@dimen/space_x5"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/iv_icon2"
                        android:layout_width="match_parent"
                        android:layout_height="32dp"
                        android:layout_gravity="center"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/space_x1"
                    android:padding="@dimen/size_2dp"
                    android:layout_marginEnd="@dimen/space_x2">

                    <TextView
                        android:id="@+id/tv_title_source"
                        style="@style/Body2MediumText.Bold.NeutralDark40"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/size_3dp"/>

                    <TextView
                        android:id="@+id/tv_subtitle_source"
                        style="@style/Caption1SmallText.Medium.NeutralLight80"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_status"
                android:layout_width="wrap_content"
                android:layout_gravity="center_vertical"
                android:visibility="gone"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/space_half"
                    android:layout_marginHorizontal="@dimen/space_x1"
                    android:textColor="@color/black3"
                    android:textAlignment="center"
                    style="@style/Caption1SmallText.SemiBold.NeutralDark40" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/space_x2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    style="@style/Caption1SmallText.Regular.NeutralDark10"
                    android:textAlignment="textStart"
                    android:text="@string/sisa_kuota_nasional_txt"/>

                <TextView
                    android:id="@+id/tv_quota_value"
                    style="@style/Caption1SmallText.Medium.NeutralDark10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/space_half"
                    android:textAlignment="textEnd"
                    android:text="Rp.1.000.000"/>

                <ImageView
                    android:id="@+id/iv_warnig"
                    android:layout_width="@dimen/space_x2"
                    android:visibility="gone"
                    android:layout_height="@dimen/space_x2"
                    android:src="@drawable/ic_warning_circle"/>
            </LinearLayout>

            <SeekBar
                android:id="@+id/sb_sbn"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:maxHeight="20dp"
                android:minHeight="20dp"
                android:progressDrawable="@drawable/custom_seekbar2"
                android:thumb="@color/transparent"
                android:splitTrack="false"/>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginVertical="@dimen/space_x1_half"
            android:background="@color/neutral_light20"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x2"
            android:orientation="horizontal"
            android:weightSum="3">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_imbah_hasil_title"
                    style="@style/Caption1SmallText.Medium.NeutralLight80"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    android:text="@string/imbah_hasil_txt"/>

                <TextView
                    android:id="@+id/tv_imbah_hasil_desc"
                    style="@style/Body3SmallText.SemiBold.NeutralDark40"
                    android:textColor="@color/success90"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    android:text="4.5%"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tenor_title"
                    style="@style/Caption1SmallText.Medium.NeutralLight80"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    android:text="@string/tenor_txt"/>

                <TextView
                    android:id="@+id/tv_tenor_desc"
                    style="@style/Body3SmallText.SemiBold.NeutralDark40"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    android:text="3 tahun"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.4"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_akhir_penawaran_title"
                    style="@style/Caption1SmallText.Medium.NeutralLight80"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/akhir_penawaran_txt"/>

                <TextView
                    android:id="@+id/tv_akhir_penawaran_desc"
                    style="@style/Body3SmallText.SemiBold.NeutralDark40"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_half"
                    android:text="4.5%"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:padding="@dimen/space_x2"
        android:backgroundTintMode="multiply"
        android:weightSum="1"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_calculator"
                android:layout_width="@dimen/space_x2"
                android:layout_height="@dimen/space_x2"
                android:layout_marginEnd="@dimen/space_x1"
                android:src="@drawable/ic_calculator"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Caption1SmallText.Bold.NeutralDark40"
                android:layout_marginEnd="2dp"
                android:text="@string/simulasikan_sbn_txt"/>

            <ImageView
                android:id="@+id/iv_mo"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_marginEnd="@dimen/space_x1"
                android:src="@drawable/ic_mo"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_panah"
            android:layout_width="@dimen/space_x3_half"
            android:layout_height="@dimen/space_x3_half"
            android:src="@drawable/ic_arrow_right_blue"/>
    </LinearLayout>
</LinearLayout>