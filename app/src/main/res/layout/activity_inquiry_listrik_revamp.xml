<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/tb_inquiry_toolbar"
        layout="@layout/toolbar_revamp_no_elevation" />


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ll_button"
        android:layout_below="@id/tb_inquiry_toolbar">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/content"
            android:fillViewport="true">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/ll_top"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="@dimen/space_x2">

                        <RelativeLayout
                            android:id="@+id/rl_parent_top_source"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <RelativeLayout
                                android:id="@+id/ll_top_source"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/ll_logo"
                                    android:layout_width="@dimen/space_x5"
                                    android:layout_height="@dimen/space_x5"
                                    android:layout_centerVertical="true"
                                    android:background="@drawable/round_history"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <ImageView
                                        android:id="@+id/iv_icon"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:clickable="true" />
                                </LinearLayout>

                                <RelativeLayout
                                    android:id="@+id/rl_inisial"
                                    android:layout_width="@dimen/space_x5"
                                    android:layout_height="@dimen/space_x5"
                                    android:layout_centerVertical="true"
                                    android:background="@drawable/round_button"
                                    android:backgroundTint="@color/highlightColor"
                                    android:orientation="vertical"
                                    android:visibility="invisible">

                                    <ImageView
                                        android:id="@+id/iv_inisial"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center" />

                                    <TextView
                                        android:id="@+id/tv_inisial"
                                        style="@style/Body3SmallText.Bold.NeutralLight10"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:text="@string/empty" />
                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/ll_sof"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_marginStart="@dimen/space_x1"
                                    android:layout_toEndOf="@+id/rl_inisial"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_recipient_name"
                                        style="@style/Body2MediumText.SemiBold.NeutralDark40"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingVertical="@dimen/size_3dp" />

                                    <TextView
                                        android:id="@+id/tv_bank_name"
                                        style="@style/Caption1SmallText.Medium.NeutralLight80"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingVertical="@dimen/size_2dp" />

                                    <TextView
                                        android:id="@+id/tv_acc_num"
                                        style="@style/Caption1SmallText.Medium.NeutralLight80"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingVertical="@dimen/size_2dp" />

                                </LinearLayout>

                            </RelativeLayout>

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_favorit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/rl_parent_top_source"
                            android:layout_marginTop="@dimen/space_x2"
                            android:background="@drawable/background_cardview_noborder"
                            android:paddingHorizontal="@dimen/space_x2"
                            android:paddingVertical="@dimen/size_10dp">

                            <TextView
                                style="@style/Body3SmallText.Medium.NeutralDark40"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerVertical="true"
                                android:layout_toStartOf="@+id/switch_save"
                                android:end="@id/switch_pp"
                                android:text="@string/add_to_saved" />

                            <Switch
                                android:id="@+id/switch_save"
                                android:layout_width="@dimen/space_x6_half"
                                android:layout_height="@dimen/space_x2_half"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginBottom="@dimen/space_x2"
                                android:thumb="@drawable/tumb_selector"
                                android:track="@drawable/track_selector"
                                tools:ignore="UseSwitchCompatOrMaterialXml" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rl_save_as"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/rl_favorit"
                            android:layout_marginTop="@dimen/space_x2"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/tv_label_save_as"
                                style="@style/Body3SmallText.SemiBold.NeutralDark40"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/save_as" />

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/til_save_as"
                                style="@style/TextInputLayoutRevamp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/tv_label_save_as"
                                android:layout_marginTop="@dimen/space_x1"
                                android:paddingVertical="@dimen/space_half"
                                app:boxStrokeColor="@color/primary_blue80"
                                app:endIconMode="clear_text"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:startIconDrawable="@drawable/ic_edit_saved_16dp"
                                app:startIconTint="@color/primary_blue80">

                                <EditText
                                    android:id="@+id/et_save_as"
                                    style="@style/Body3SmallText.Regular.NeutralDark40"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:importantForAutofill="no"
                                    android:inputType="text"
                                    android:maxLength="30"
                                    android:maxLines="1"
                                    android:textColorHint="@color/neutral_light40" />

                            </com.google.android.material.textfield.TextInputLayout>
                        </RelativeLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/cl_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/rl_parent_top_source"
                            android:layout_marginTop="@dimen/space_x2"
                            android:background="@drawable/background_cardview_information"
                            android:padding="@dimen/space_x1_half"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/iv_info"
                                android:layout_width="@dimen/space_x2"
                                android:layout_height="@dimen/space_x2"
                                android:contentDescription="@string/information"
                                android:src="@drawable/info_biru"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />


                            <TextView
                                android:id="@+id/tv_info"
                                style="@style/Caption1SmallText.Medium.NeutralDark20"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/space_x1"
                                android:contentDescription="@string/information"
                                android:text="@string/added_to_saved"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/iv_info"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ll_nominal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/space_x2">

                        <TextView
                            style="@style/Body2MediumText.Bold.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Pilih Nominal Pembelian"
                            android:layout_marginHorizontal="@dimen/space_x2"/>


                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_nominal_token"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginHorizontal="@dimen/space_x1"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layoutAnimation="@anim/layout_animation_fall_down"
                            android:overScrollMode="never" />

                    </LinearLayout>

                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/linear_banner_agf"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/space_x1"
                            android:background="@color/light_10" />

                        <LinearLayout
                            android:id="@+id/linear_info_schedule"
                            android:padding="@dimen/space_x2"
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/title_layout_transfer_terjadwal"
                                style="@style/Body2MediumText.Bold.NeutralDark40"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentTop="true"
                                android:layout_toStartOf="@id/switch_agf"
                                android:paddingVertical="@dimen/space_half"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />


                            <TextView
                                android:id="@+id/desc_layout_transfer_terjadwal"
                                style="@style/Caption1SmallText.Medium.NeutralDark20"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/space_x1"
                                android:lineSpacingExtra="@dimen/size_3dp"
                                android:text="@string/layout_agf_desc"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/space_x1"
                            android:background="@color/light_10" />
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/ll_summary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <include
                            android:id="@+id/inc_sumber_dana"
                            layout="@layout/sumber_dana_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />
                    </LinearLayout>

                </LinearLayout>


            </RelativeLayout>
        </ScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/ll_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        app:cardElevation="@dimen/space_x2"
        app:cardCornerRadius="0dp"
        android:orientation="vertical">
        <RelativeLayout
            android:id="@+id/cv_pulsa"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:backgroundTint="@color/white"
            android:outlineProvider="bounds"
            app:cardElevation="@dimen/margin_elevation_card"
            app:cardUseCompatPadding="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/bg_with_shadow"
                android:orientation="horizontal"
                android:padding="@dimen/space_x2"
                android:weightSum="2">

                <LinearLayout
                    android:id="@+id/ll_total_bayar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.2"
                    android:gravity="left"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_detail_pembayaran"
                            style="@style/Caption1SmallText.Medium.NeutralLight80"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_payment" />

                        <ImageView

                            android:layout_width="@dimen/space_x2"
                            android:layout_height="@dimen/space_x2"
                            android:layout_marginStart="@dimen/space_half"
                            android:src="@drawable/ic_arrow_down_blue"
                            android:rotation="180"/>
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tv_total_pembayaran"
                        style="@style/Body1LargeText.Bold.NeutralDark40"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_half"
                        android:text="@string/empty" />
                    <TextView
                        android:id="@+id/tv_saldo_tdk_cukup_act"
                        style="@style/Caption1SmallText.Medium.Error"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_half"
                        android:text="@string/saldo_tdk_cukup"
                        android:visibility="gone"
                        />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="0.8"
                    android:gravity="right|center_vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnSubmit"
                        style="@style/ButtonPrimaryRevampMaterial"
                        android:layout_width="120dp"
                        android:layout_height="@dimen/space_x6"
                        android:text="@string/buy"
                        android:textAllCaps="false" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </androidx.cardview.widget.CardView>


</RelativeLayout>