<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#eef2f6"
    android:fitsSystemWindows="true"
    tools:context="id.co.bri.brimo.ui.activities.registrasi.RegisTutorialFotoActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_marginBottom="@dimen/space_x11"
        android:background="@color/white">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="vertical">
            <!-- Informasi Data Diri -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginTop="@dimen/space_x2_half"
                android:layout_marginEnd="@dimen/space_x2"
                android:layout_marginBottom="@dimen/size_20dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/space_half"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/BodyMediumText.Bold.BluePrimary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Pilih Profil Risiko" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:text="3/3"
                        android:textSize="14dp" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/BodySmallText.Medium.Black"
                    android:text="Pilih Jenis Profil Risiko yang sesuai dengan caramu berinvestasi." />

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/ly_rendah"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:padding="@dimen/space_x2"
                        android:background="@drawable/bg_white_border_grey"
                        android:layout_width="match_parent"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:orientation="horizontal"
                            android:layout_height="wrap_content">

                            <RadioButton
                                android:clickable="false"
                                android:buttonTint="@color/colorBackgroundPin"
                                android:id="@+id/rb_rendah"
                                android:layout_marginEnd="@dimen/space_x1_half"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                style="@style/BodyMediumText.DemiBold.Black"
                                android:layout_width="wrap_content"
                                android:text="Rendah"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <TextView
                            style="@style/BodySmallText.Medium.Black"
                            android:layout_width="match_parent"
                            android:text="Berencana investasi kurang dari 1 Tahun. Fokus untuk menghindari kerugian"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_moderat"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:padding="@dimen/space_x2"
                        android:background="@drawable/bg_white_border_grey"
                        android:layout_width="match_parent"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:orientation="horizontal"
                            android:layout_height="wrap_content">

                            <RadioButton
                                android:clickable="false"
                                android:id="@+id/rb_moderat"
                                android:layout_marginEnd="@dimen/space_x1_half"
                                android:layout_width="wrap_content"
                                android:buttonTint="@color/colorBackgroundPin"
                                android:layout_height="wrap_content"/>
                            <TextView
                                style="@style/BodyMediumText.DemiBold.Black"
                                android:layout_width="wrap_content"
                                android:text="Moderat"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <TextView
                            style="@style/BodySmallText.Medium.Black"
                            android:layout_width="match_parent"
                            android:text="Berencana investasi kurang dari 2-3 Tahun. Risiko kerugian &amp; keuntungan seimbang"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_tinggi"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:padding="@dimen/space_x2"
                        android:background="@drawable/bg_white_border_grey"
                        android:layout_width="match_parent"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:orientation="horizontal"
                            android:layout_height="wrap_content">

                            <RadioButton
                                android:clickable="false"
                                android:buttonTint="@color/colorBackgroundPin"
                                android:id="@+id/rb_tinggi"
                                android:layout_marginEnd="@dimen/space_x1_half"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                style="@style/BodyMediumText.DemiBold.Black"
                                android:layout_width="wrap_content"
                                android:text="Tinggi"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <TextView
                            style="@style/BodySmallText.Medium.Black"
                            android:layout_width="match_parent"
                            android:text="Berencana investasi di atas 5 tahun. Fokus memaksimalkan keuntungan"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </ScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorButtonGrey"
        android:orientation="vertical">

        <View
            android:id="@+id/view2"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_gravity="top"
            android:background="@drawable/toolbar_dropshadow" />

        <Button
            android:id="@+id/btnSubmit"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:background="@drawable/rounded_button_blue"
            android:fontFamily="@font/avenir_next_bold"
            android:text="Lanjutkan"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/space_x2"

            />
    </LinearLayout>
</RelativeLayout>