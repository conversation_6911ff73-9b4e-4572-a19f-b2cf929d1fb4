<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:theme="@style/AppThemeBlueBar"
    tools:context="id.co.bri.brimo.ui.activities.CatatanKeuanganActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="@dimen/space_half"
            android:orientation="vertical"
            android:outlineProvider="bounds">

            <include
                android:id="@+id/tbCatatanKeuangan"
                layout="@layout/toolbar_not_elevated" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_x8"
                android:layout_gravity="center_vertical"
                android:background="@color/toolbar_blue"
                android:paddingTop="@dimen/space_x1_half">

                <com.ogaclejapan.smarttablayout.SmartTabLayout
                    android:id="@+id/tabCatatanKeuangan"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_x4_half"
                    android:layout_alignParentTop="true"
                    android:layout_marginHorizontal="10dp"
                    android:background="@drawable/bg_pfm_menu"
                    android:clipToPadding="false"
                    app:cardElevation="@dimen/margin_elevation_card"
                    app:stl_defaultTabBackground="@android:color/transparent"
                    app:stl_defaultTabTextAllCaps="false"
                    app:stl_defaultTabTextSize="14dp"
                    app:stl_distributeEvenly="true"
                    app:stl_dividerColor="@color/transparent"
                    app:stl_dividerThickness="0dp"
                    app:stl_indicatorColor="@color/white"
                    app:stl_indicatorCornerRadius="@dimen/space_x3"
                    app:stl_indicatorGravity="center"
                    app:stl_indicatorInterpolation="linear"
                    app:stl_indicatorThickness="@dimen/space_x4_half"
                    app:stl_titleOffset="auto_center"
                    app:stl_underlineThickness="0dp" />
            </FrameLayout>
        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/vpCatatanKeuangan"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/ll_pemasukan"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/avenir_next_medium"
                            android:text="Pemasukan"
                            android:textAlignment="center"
                            android:textColor="@color/colorPfmFontTotal"
                            android:textSize="@dimen/size_pfm_empty_text" />

                        <TextView
                            android:id="@+id/tvAmountPemasukan"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/avenir_next_bold"
                            android:gravity="center"
                            android:text="Rp0"
                            android:textAlignment="center"
                            android:textColor="@color/colorPfmFontTotal"
                            android:textSize="@dimen/size_pfm_empty_text"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:src="@drawable/divider_vertical" />

                    <LinearLayout
                        android:id="@+id/ll_pengeluaran"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/avenir_next_medium"
                            android:text="Pengeluaran"
                            android:textAlignment="center"
                            android:textColor="@color/colorPfmFontTotal"
                            android:textSize="@dimen/size_pfm_empty_text" />

                        <TextView
                            android:id="@+id/tvAmountPengeluaran"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/avenir_next_bold"
                            android:gravity="center"
                            android:text="Rp0"
                            android:textAlignment="center"
                            android:textColor="@color/colorPfmFontTotal"
                            android:textSize="@dimen/size_pfm_empty_text"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="6dp"
                android:layout_marginTop="60dp"
                android:background="@color/colorButtonGrey"
                android:visibility="gone">

            </LinearLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>
</FrameLayout>

