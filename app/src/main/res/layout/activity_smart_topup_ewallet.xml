<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.saldodompetdigital.HubungkanDompetDigitalActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:elevation="@dimen/_4sdp"
        android:outlineProvider="bounds"
        android:textAlignment="center"
        app:layout_collapseMode="pin"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <TextView
            android:id="@+id/textTitle"
            style="@style/ToolbarTitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/avenir_next_bold"
            android:textColor="@color/colorTextWhite"
            android:textSize="16dp" />
    </androidx.appcompat.widget.Toolbar>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/layout_button"
        android:layout_below="@id/toolbar"
        android:background="@color/colorTextWhite">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="109dp"
                        android:background="@drawable/backgroun_biru_muda_atas" />

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16sdp"
                        android:layout_marginTop="40dp"
                        android:layout_marginEnd="@dimen/_16sdp"
                        android:layout_marginBottom="2dp"
                        android:clipToPadding="true"
                        app:cardCornerRadius="@dimen/_8sdp"
                        app:contentPadding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/iv_logo_dompet"
                                android:layout_width="@dimen/_56sdp"
                                android:layout_height="@dimen/_56sdp"
                                android:layout_marginRight="8dp"
                                android:scaleType="fitXY" />l

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tv_pemilik_dompet_digital"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/avenir_next_bold"
                                    android:singleLine="true"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_medium_text" />

                                <TextView
                                    android:id="@+id/tv_info_dompet_digital"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:singleLine="true"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_small_text" />
                            </LinearLayout>
                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/lldetail"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="17dp"
                    android:layout_marginTop="@dimen/_16sdp"
                    android:layout_marginEnd="17dp"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="12dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/status_smart_top_up"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_small_text" />

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="8dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="18dp"
                                        android:layout_marginTop="4dp"
                                        android:layout_marginBottom="12dp"
                                        android:orientation="horizontal">

                                        <ImageView
                                            android:id="@+id/iv_icon_status_smart_top_up"
                                            android:layout_width="@dimen/size_20dp"
                                            android:layout_height="@dimen/size_20dp"
                                            android:layout_marginEnd="5dp"
                                            android:src="@drawable/ic_dot_red"
                                            android:visibility="visible" />

                                        <TextView
                                            android:id="@+id/tv_status_smart_top_up"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/avenir_next_demi"
                                            android:textColor="@color/black3"
                                            android:textSize="16dp"
                                            tools:text="-" />
                                    </LinearLayout>

                                </RelativeLayout>


                            </LinearLayout>

                            <Switch
                                android:id="@+id/switch_smart_top_up"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:thumb="@drawable/tumb_selector"
                                android:track="@drawable/track_selector" />

                        </LinearLayout>

                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent2Color" />
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible">

                            <LinearLayout
                                android:id="@+id/ll_minimal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="12dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/saldo_min"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_small_text" />

                                <TextView
                                    android:id="@+id/tv_saldo_minimal"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="4dp"
                                    android:layout_marginBottom="12dp"
                                    android:fontFamily="@font/avenir_next_demi"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_medium_text"
                                    tools:text="-" />
                            </LinearLayout>

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_marginStart="50dp"
                                android:layout_marginTop="18dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="22dp"
                                android:src="@drawable/ic_arrow_down_blue" />
                        </RelativeLayout>


                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent2Color" />


                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent2Color" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="visible">

                            <LinearLayout
                                android:id="@+id/ll_nominal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="12dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/nominal_top_up"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_small_text" />

                                <TextView
                                    android:id="@+id/tv_nominal_top_up"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="4dp"
                                    android:layout_marginBottom="12dp"
                                    android:fontFamily="@font/avenir_next_demi"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_medium_text"
                                    tools:text="-" />
                            </LinearLayout>

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_marginStart="50dp"
                                android:layout_marginTop="18dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="22dp"
                                android:src="@drawable/ic_arrow_down_blue" />
                        </RelativeLayout>

                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent2Color" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:id="@+id/ll_frekuensi"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="12dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/frekuensi_top_up"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_small_text" />

                                <TextView
                                    android:id="@+id/tv_frekuensi_top_up"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="18dp"
                                    android:layout_marginTop="4dp"
                                    android:layout_marginBottom="12dp"
                                    android:fontFamily="@font/avenir_next_demi"
                                    android:textColor="@color/black3"
                                    android:textSize="@dimen/body_medium_text"
                                    tools:text="-" />
                            </LinearLayout>

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_marginStart="50dp"
                                android:layout_marginTop="18dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="22dp"
                                android:src="@drawable/ic_arrow_down_blue" />
                        </RelativeLayout>

                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent2Color" />


                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="1dp"
                            android:background="@color/accent3Color" />

                        <RelativeLayout
                            android:id="@+id/rl_informasi"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginHorizontal="5dp"
                            android:layout_marginTop="@dimen/_16sdp"
                            android:layout_marginBottom="10dp"
                            android:background="@drawable/rounded_line_blue"
                            android:outlineProvider="bounds">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:backgroundTint="#eef2f6"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="9dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="7dp"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="18dp"
                                        android:layout_height="18dp"
                                        android:src="@drawable/ic_info"
                                        android:textColor="#00529c" />

                                    <TextView
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="8dp"
                                        android:layout_marginTop="3dp"
                                        android:layout_marginEnd="20dp"
                                        android:fontFamily="@font/avenir_next_bold"
                                        android:gravity="left"
                                        android:text="Informasi"
                                        android:textColor="#00529c"
                                        android:textSize="10.5dp" />

                                    <ImageView
                                        android:id="@+id/iv_close"
                                        android:layout_width="14dp"
                                        android:layout_height="14dp"
                                        android:layout_gravity="right"
                                        android:layout_marginStart="-16dp"
                                        android:src="@drawable/ic_close_info"
                                        android:textColor="#00529c"
                                        android:visibility="gone" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="7dp"
                                    android:layout_marginBottom="10dp"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/info_form"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="9dp"
                                        android:layout_marginEnd="7dp"
                                        android:fontFamily="@font/avenir_next_medium"
                                        android:text="@string/txt_info_smart_topup"
                                        android:textColor="#202020"
                                        android:textSize="@dimen/micro_text" />
                                </LinearLayout>
                            </LinearLayout>

                        </RelativeLayout>

                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/layout_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/accent3Color"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_submit"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:alpha="0.3"
            android:background="@drawable/rounded_button_blue"
            android:enabled="false"
            android:fontFamily="@font/avenir_next_bold"
            android:text="Simpan"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="16dp" />
    </LinearLayout>

</RelativeLayout>