<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="id.co.bri.brimo.ui.activities.TambahDaftarCicilanActivity">

    <include
        android:id="@+id/tb_input_cicilan"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_input_cicilan">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/space_x2">

            <TextView
                style="@style/BodyMediumText.Bold.BluePrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/judulTambahCicilan" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x3"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="@dimen/space_x5"
                    android:layout_height="@dimen/space_x5">

                    <ImageView
                        android:id="@+id/iv_cicilan"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/size_2dp"
                        android:src="@drawable/cicilan" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_x2"
                    android:orientation="vertical"
                    tools:ignore="RtlSymmetry">

                    <TextView
                        style="@style/BodySmallText.Medium.Grey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/size_2dp"
                        android:text="@string/cicilanTextview2" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/tiBank"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/size_9dp"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false">

                        <EditText
                            android:id="@+id/etJenisCicilan"
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@android:color/transparent"
                            android:drawableEnd="@drawable/ic_arrow_down_blue"
                            android:focusable="false"
                            android:hint="@string/cicilanEditText2"
                            android:inputType="none|textNoSuggestions"
                            android:maxLines="1"
                            android:textColorHint="@color/colorAccent" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
            </LinearLayout>

            <View
                android:id="@+id/view3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_1dp"
                android:background="@color/accent2Color" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x2"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="@dimen/space_x5"
                    android:layout_height="@dimen/space_x5">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/size_2dp"
                        android:src="@drawable/nama" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_x2"
                    android:orientation="vertical"
                    tools:ignore="RtlSymmetry">

                    <TextView
                        style="@style/BodySmallText.Medium.Grey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/size_2dp"
                        android:text="@string/cicilanTextview1" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/tiAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/size_9dp"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false">

                        <EditText
                            android:id="@+id/etNoPelanggan"
                            style="@style/BodyMediumText.DemiBold.Black"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@android:color/transparent"
                            android:hint="@string/cicilanEditText1"
                            android:inputType="number"
                            android:maxLength="30"
                            android:maxLines="1"
                            android:textColorHint="@color/colorAccent" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_1dp"
                android:background="@color/accent2Color" />
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/accent3Color"
        android:orientation="vertical">

        <Button
            android:id="@+id/btnSubmit"
            style="@style/BodyMediumText.Bold.White"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            android:layout_margin="@dimen/space_x2"
            android:alpha="0.3"
            android:background="@drawable/rounded_button_blue"
            android:enabled="false"
            android:text="@string/buttonLanjut"
            android:textAllCaps="false" />
    </LinearLayout>
</RelativeLayout>