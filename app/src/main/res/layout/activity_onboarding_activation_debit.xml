<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_full_grey"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.activationdebit.ActivationDebitOnboardingActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_revamp_no_elevation"/>

    <View
        android:id="@+id/viewBgPattern"
        android:layout_width="match_parent"
        android:layout_height="@dimen/space_x41"
        android:background="@drawable/ic_bg_pattern"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgOnboarding"
        android:layout_width="@dimen/space_x20"
        android:layout_height="@dimen/space_x20"
        android:layout_marginTop="@dimen/space_x5"
        tools:src="@tools:sample/avatars"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBriefText"
        style="@style/Body2MediumText.Bold"
        android:layout_width="@dimen/item_offset"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_x3"
        android:layout_marginHorizontal="@dimen/space_x3"
        tools:text="@tools:sample/lorem"
        android:textColor="@color/white"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/imgOnboarding"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vpOnboarding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/item_offset"
        android:layout_marginHorizontal="@dimen/space_x2"
        android:layout_marginTop="@dimen/space_x1_half"
        android:layout_marginBottom="@dimen/space_x2"
        app:layout_constraintTop_toBottomOf="@id/tvBriefText"
        app:layout_constraintBottom_toTopOf="@id/llBriefInfo" />

    <LinearLayout
        android:id="@+id/llBriefInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintVertical_bias=".8"
        app:layout_constraintTop_toBottomOf="@id/tvBriefText"
        app:layout_constraintBottom_toTopOf="@id/dotsIndicator"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCardType"
            style="@style/Body3SmallText.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/action_debit_card_type"
            android:textColor="@color/primary_blue80"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgChevronRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_arrow_right_blue"
            android:layout_marginStart="@dimen/space_half"/>

    </LinearLayout>

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dotsIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/space_x2_half"
        app:dotsColor="@color/color_blue_88BFEC"
        app:dotsCornerRadius="8dp"
        app:dotsSize="10dp"
        app:dotsSpacing="4dp"
        app:dotsWidthFactor="2.5"
        app:progressMode="false"
        app:selectedDotColor="@color/primary_blue80"
        app:layout_constraintBottom_toTopOf="@id/layoutButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <LinearLayout
        android:id="@+id/layoutButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_x2"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnActivateNow"
            style="@style/ButtonPrimaryRevamp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            android:layout_margin="@dimen/space_x2"
            android:text="@string/aktivasi_sekarang" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>