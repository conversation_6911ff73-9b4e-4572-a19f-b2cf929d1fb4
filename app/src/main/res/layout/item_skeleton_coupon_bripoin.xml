<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <androidx.constraintlayout.widget.Guideline
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent=".1"/>

    <View
        android:id="@+id/ic_profile"
        android:layout_width="@dimen/space_x7"
        android:layout_height="@dimen/space_x7"
        android:layout_marginStart="@dimen/space_x2"
        android:background="@drawable/bg_skeleton_oval"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <View
        android:id="@+id/viewTitle"
        android:layout_width="@dimen/space_x30"
        android:layout_height="@dimen/space_x3"
        android:background="@drawable/bg_skeleton_rounded"
        android:layout_marginStart="@dimen/space_x2"
        android:textSize="@dimen/size_text_16sp"
        app:layout_constraintTop_toTopOf="@id/ic_profile"
        app:layout_constraintStart_toEndOf="@id/ic_profile"/>

    <View
        android:layout_width="@dimen/space_x30"
        android:layout_height="@dimen/space_x3"
        android:layout_gravity="center_vertical"
        android:background="@drawable/bg_skeleton_rounded"
        android:textSize="@dimen/size_text_14sp"
        app:layout_constraintBottom_toBottomOf="@id/ic_profile"
        app:layout_constraintStart_toStartOf="@id/viewTitle" />

</androidx.constraintlayout.widget.ConstraintLayout>