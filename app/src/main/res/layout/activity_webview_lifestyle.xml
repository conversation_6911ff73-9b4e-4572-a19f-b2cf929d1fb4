<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_back_close" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_below="@id/toolbar"
        android:layout_above="@id/rl_powered"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <ProgressBar
            android:id="@+id/progressBar1"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="@dimen/_50sdp"
            android:layout_height="@dimen/_50sdp"
            android:indeterminate="false"
            android:layout_gravity="center"
            android:visibility="gone"/>

        <WebView
            android:id="@+id/webview_lifestyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/rl_powered"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/neutral_light10"
        android:paddingVertical="@dimen/space_x2"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/ll_powered"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_title_powered"
                style="@style/Caption1SmallText.Medium.NeutralLight80"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical" />

            <ImageView
                android:id="@+id/iv_powered"
                android:layout_width="40dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/space_half"
                android:contentDescription="@null" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_desc_powered"
            style="@style/Caption1SmallText.Medium.NeutralLight80"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_powered"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/space_half"
            android:visibility="gone" />

    </RelativeLayout>

</RelativeLayout>