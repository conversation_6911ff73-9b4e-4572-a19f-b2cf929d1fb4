<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/item_layout_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/size_2dp"
            android:paddingTop="@dimen/size_2dp"
            android:paddingBottom="@dimen/space_x1"
            android:gravity="center">

            <RelativeLayout
                android:id="@+id/rl_inisial_saved"
                android:layout_width="@dimen/space_x5"
                android:layout_height="@dimen/space_x5"
                android:backgroundTint="@color/headerColor"
                android:orientation="vertical"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/iv_inisial"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/round_background"
                    android:backgroundTint="@color/headerColor" />

                <TextView
                    android:id="@+id/tv_inisial_saved"
                    style="@style/BodySmallText.DemiBold.White"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_logo_saved"
                android:layout_width="@dimen/space_x5"
                android:layout_height="@dimen/space_x5"
                android:background="@drawable/round_history"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/iv_icon_saved"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:clickable="true" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_saved"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/space_x1"
                android:layout_weight="1"
                android:background="@color/transparent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_name"
                    style="@style/BodyMediumText.DemiBold.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="@dimen/space_half"
                    android:text="@string/empty"
                    android:visibility="gone"/>
                <TextView
                    android:id="@+id/tv_title"
                    style="@style/BodyMediumText.DemiBold.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="@dimen/space_half"
                    android:text="@string/empty" />

                <TextView
                    android:id="@+id/tv_nomor"
                    style="@style/BodySmallText.Medium.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/empty"
                    android:paddingBottom="@dimen/space_half" />
            </LinearLayout>

            <ImageView
                android:id="@+id/star_favorit_saved"
                android:layout_width="@dimen/space_x3"
                android:layout_height="@dimen/space_x3"
                android:layout_gravity="end|center"
                android:src="@drawable/ic_star"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/menu_saved"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center"
                android:background="@color/transparent"
                android:src="@drawable/ic_more_vert_black_24dp"
                android:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/kategoriHorizontalDivider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/item_layout_background"
            android:layout_marginBottom="@dimen/space_x1">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_1dp"
                android:layout_gravity="center"
                android:background="@color/accent2Color" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>